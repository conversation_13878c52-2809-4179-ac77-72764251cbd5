using System.Collections;
using System.Collections.Generic;
using UnityEngine;

public class BannerAD : MonoBehaviour
{
 public static BannerAD Instance;
    public void Awake()
    {
        Instance = this;
        DontDestroyOnLoad(gameObject);

      
        if (AdsController.Instance != null)
        {
            AdsController.Instance.ShowBannerAd_Admob(0);
            AdsController.Instance.ShowBannerAd_Admob(1);
        }
        else
        {
           
            StartCoroutine(TryShowBannerWhenReady());
        }
    }

    private IEnumerator TryShowBannerWhenReady()
    {
       
        while (AdsController.Instance == null)
        {
            yield return new WaitForSeconds(0.5f);
        }

      
        AdsController.Instance.ShowBannerAd_Admob(0);
        AdsController.Instance.ShowBannerAd_Admob(1);
    }
    void Start()
    {
        Screen.sleepTimeout = SleepTimeout.NeverSleep;
    }
}
