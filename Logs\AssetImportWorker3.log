Using pre-set license
Built from '2021.3/staging' branch; Version is '2021.3.45f1 (0da89fac8e79) revision 895135'; Using compiler version '192829333'; Build Type 'Release'
OS: 'Windows 10  (10.0.19045) 64bit ProfessionalWorkstation' Language: 'en' Physical Memory: 32684 MB
BatchMode: 1, IsHumanControllingUs: 0, StartBugReporterOnCrash: 0, Is64bit: 1, IsPro: 1

COMMAND LINE ARGUMENTS:
C:\Program Files\Unity\Hub\Editor\2021.3.45f1\Editor\Unity.exe
-adb2
-batchMode
-noUpm
-name
AssetImportWorker3
-projectPath
D:/My Project/Tractor Simulator Cargo Games (V1.9)smg/Tractor Simulator Cargo
-logFile
Logs/AssetImportWorker3.log
-srvPort
51145
Successfully changed project path to: D:/My Project/Tractor Simulator Cargo Games (V1.9)smg/Tractor Simulator Cargo
D:/My Project/Tractor Simulator Cargo Games (V1.9)smg/Tractor Simulator Cargo
[UnityMemory] Configuration Parameters - Can be set up in boot.config
    "memorysetup-bucket-allocator-granularity=16"
    "memorysetup-bucket-allocator-bucket-count=8"
    "memorysetup-bucket-allocator-block-size=33554432"
    "memorysetup-bucket-allocator-block-count=8"
    "memorysetup-main-allocator-block-size=16777216"
    "memorysetup-thread-allocator-block-size=16777216"
    "memorysetup-gfx-main-allocator-block-size=16777216"
    "memorysetup-gfx-thread-allocator-block-size=16777216"
    "memorysetup-cache-allocator-block-size=4194304"
    "memorysetup-typetree-allocator-block-size=2097152"
    "memorysetup-profiler-bucket-allocator-granularity=16"
    "memorysetup-profiler-bucket-allocator-bucket-count=8"
    "memorysetup-profiler-bucket-allocator-block-size=33554432"
    "memorysetup-profiler-bucket-allocator-block-count=8"
    "memorysetup-profiler-allocator-block-size=16777216"
    "memorysetup-profiler-editor-allocator-block-size=1048576"
    "memorysetup-temp-allocator-size-main=16777216"
    "memorysetup-job-temp-allocator-block-size=2097152"
    "memorysetup-job-temp-allocator-block-size-background=1048576"
    "memorysetup-job-temp-allocator-reduction-small-platforms=262144"
    "memorysetup-temp-allocator-size-background-worker=32768"
    "memorysetup-temp-allocator-size-job-worker=262144"
    "memorysetup-temp-allocator-size-preload-manager=33554432"
    "memorysetup-temp-allocator-size-nav-mesh-worker=65536"
    "memorysetup-temp-allocator-size-audio-worker=65536"
    "memorysetup-temp-allocator-size-cloud-worker=32768"
    "memorysetup-temp-allocator-size-gi-baking-worker=262144"
    "memorysetup-temp-allocator-size-gfx=262144"
Player connection [20008] Host "[IP] *********** [Port] 0 [Flags] 2 [Guid] 3082351444 [EditorId] 3082351444 [Version] 1048832 [Id] WindowsEditor(7,DESKTOP-GMG2SOO) [Debug] 1 [PackageName] WindowsEditor [ProjectName] Editor" joined multi-casting on [***********:54997]...

Player connection [20008] Host "[IP] *********** [Port] 0 [Flags] 2 [Guid] 3082351444 [EditorId] 3082351444 [Version] 1048832 [Id] WindowsEditor(7,DESKTOP-GMG2SOO) [Debug] 1 [PackageName] WindowsEditor [ProjectName] Editor" joined alternative multi-casting on [***********:34997]...

[Physics::Module] Initialized MultithreadedJobDispatcher with {0} workers.
Refreshing native plugins compatible for Editor in 116.05 ms, found 4 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Initialize engine version: 2021.3.45f1 (0da89fac8e79)
[Subsystems] Discovering subsystems at path C:/Program Files/Unity/Hub/Editor/2021.3.45f1/Editor/Data/Resources/UnitySubsystems
[Subsystems] Discovering subsystems at path D:/My Project/Tractor Simulator Cargo Games (V1.9)smg/Tractor Simulator Cargo/Assets
GfxDevice: creating device client; threaded=0; jobified=0
Direct3D:
    Version:  Direct3D 11.0 [level 11.0]
    Renderer: NVIDIA Quadro K2200 (ID=0x13ba)
    Vendor:   NVIDIA
    VRAM:     4035 MB
    Driver:   10.18.13.5330
Initialize mono
Mono path[0] = 'C:/Program Files/Unity/Hub/Editor/2021.3.45f1/Editor/Data/Managed'
Mono path[1] = 'C:/Program Files/Unity/Hub/Editor/2021.3.45f1/Editor/Data/MonoBleedingEdge/lib/mono/unityjit-win32'
Mono config path = 'C:/Program Files/Unity/Hub/Editor/2021.3.45f1/Editor/Data/MonoBleedingEdge/etc'
Using monoOptions --debugger-agent=transport=dt_socket,embedding=1,server=y,suspend=n,address=127.0.0.1:56948
Begin MonoManager ReloadAssembly
Registering precompiled unity dll's ...
Register platform support module: C:/Program Files/Unity/Hub/Editor/2021.3.45f1/Editor/Data/PlaybackEngines/AndroidPlayer/UnityEditor.Android.Extensions.dll
Register platform support module: C:/Program Files/Unity/Hub/Editor/2021.3.45f1/Editor/Data/PlaybackEngines/WindowsStandaloneSupport/UnityEditor.WindowsStandalone.Extensions.dll
Registered in 0.013076 seconds.
Native extension for WindowsStandalone target not found
Native extension for Android target not found
Android Extension - Scanning For ADB Devices 397 ms
Refreshing native plugins compatible for Editor in 91.67 ms, found 4 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Mono: successfully reloaded assembly
- Completed reload, in  1.572 seconds
Domain Reload Profiling:
	ReloadAssembly (1573ms)
		BeginReloadAssembly (174ms)
			ExecutionOrderSort (0ms)
			DisableScriptedObjects (0ms)
			BackupInstance (0ms)
			ReleaseScriptingObjects (0ms)
			CreateAndSetChildDomain (2ms)
		EndReloadAssembly (1209ms)
			LoadAssemblies (170ms)
			RebuildTransferFunctionScriptingTraits (0ms)
			SetupTypeCache (206ms)
			ReleaseScriptCaches (0ms)
			RebuildScriptCaches (53ms)
			SetupLoadedEditorAssemblies (877ms)
				LogAssemblyErrors (0ms)
				InitializePlatformSupportModulesInManaged (533ms)
				SetLoadedEditorAssemblies (1ms)
				RefreshPlugins (92ms)
				BeforeProcessingInitializeOnLoad (2ms)
				ProcessInitializeOnLoadAttributes (177ms)
				ProcessInitializeOnLoadMethodAttributes (73ms)
				AfterProcessingInitializeOnLoad (0ms)
				EditorAssembliesLoaded (0ms)
			ExecutionOrderSort2 (0ms)
			AwakeInstancesAfterBackupRestoration (0ms)
Platform modules already initialized, skipping
Registering precompiled user dll's ...
Registered in 0.015786 seconds.
Begin MonoManager ReloadAssembly
Native extension for WindowsStandalone target not found
Native extension for Android target not found
Refreshing native plugins compatible for Editor in 1.29 ms, found 4 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Package Manager log level set to [2]
[Package Manager] Server::EnsureServerProcessIsRunning -- launch failed, reason: Unity was launched with the -noUpm command-line argument
[Package Manager] Cannot connect to Unity Package Manager local server
Mono: successfully reloaded assembly
- Completed reload, in  2.571 seconds
Domain Reload Profiling:
	ReloadAssembly (2572ms)
		BeginReloadAssembly (258ms)
			ExecutionOrderSort (0ms)
			DisableScriptedObjects (12ms)
			BackupInstance (0ms)
			ReleaseScriptingObjects (0ms)
			CreateAndSetChildDomain (37ms)
		EndReloadAssembly (2104ms)
			LoadAssemblies (193ms)
			RebuildTransferFunctionScriptingTraits (0ms)
			SetupTypeCache (398ms)
			ReleaseScriptCaches (1ms)
			RebuildScriptCaches (112ms)
			SetupLoadedEditorAssemblies (1394ms)
				LogAssemblyErrors (0ms)
				InitializePlatformSupportModulesInManaged (26ms)
				SetLoadedEditorAssemblies (1ms)
				RefreshPlugins (1ms)
				BeforeProcessingInitializeOnLoad (100ms)
				ProcessInitializeOnLoadAttributes (1215ms)
				ProcessInitializeOnLoadMethodAttributes (38ms)
				AfterProcessingInitializeOnLoad (13ms)
				EditorAssembliesLoaded (0ms)
			ExecutionOrderSort2 (0ms)
			AwakeInstancesAfterBackupRestoration (9ms)
Platform modules already initialized, skipping
========================================================================
Worker process is ready to serve import requests
Launched and connected shader compiler UnityShaderCompiler.exe after 0.07 seconds
Refreshing native plugins compatible for Editor in 3.94 ms, found 4 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 2304 Unused Serialized files (Serialized files now loaded: 0)
Unloading 172 unused Assets / (0.7 MB). Loaded Objects now: 2716.
Memory consumption went from 122.3 MB to 121.6 MB.
Total: 13.262000 ms (FindLiveObjects: 1.631600 ms CreateObjectMapping: 0.402800 ms MarkObjects: 10.326300 ms  DeleteObjects: 0.899500 ms)

AssetImportParameters requested are different than current active one (requested -> active):
  custom:video-decoder-ogg-theora: a1e56fd34408186e4bbccfd4996cb3dc -> 
  custom:container-muxer-webm: aa71ff27fc2769a1b78a27578f13a17b -> 
  custom:container-demuxer-webm: 4f35f7cbe854078d1ac9338744f61a02 -> 
  custom:video-encoder-webm-vp8: eb34c28f22e8b96e1ab97ce403110664 -> 
  custom:CustomObjectIndexerAttribute: bc11b3a6c3213fcdd17b65e7da85e133 -> 
  custom:audio-encoder-webm-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
  custom:framework-win-MediaFoundation: 216162199b28c13a410421893ffa2e32 -> 
  custom:SearchIndexIgnoredProperties: e643bd26f0fe6173181afceb89e7c659 -> 
  custom:container-demuxer-ogg: 62fdf1f143b41e24485cea50d1cbac27 -> 
  custom:video-decoder-webm-vp8: 9c59270c3fd7afecdb556c50c9e8de78 -> 
  custom:audio-decoder-ogg-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
========================================================================
Received Import Request.
  Time since last request: 3361.354057 seconds.
  path: Assets/RealisticCarControllerV3/Textures/Dashboard/ABS.png
  artifactKey: Guid(7e89db381e56bcc49a338c5da80f2a87) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Number of updated assets reloaded before import = 0
Start importing Assets/RealisticCarControllerV3/Textures/Dashboard/ABS.png using Guid(7e89db381e56bcc49a338c5da80f2a87) Importer(815301076,1909f56bfc062723c751e8b465ee728b)  -> (artifact id: '5e3a83022d93eb0beda138dc6b54ca2c') in 0.274541 seconds 
Number of asset objects unloaded after import = 3
========================================================================
Received Import Request.
  Time since last request: 0.000031 seconds.
  path: Assets/tractor ui/changis/ACCPET.png
  artifactKey: Guid(e8381f1005a773d43954b940d850d6fe) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Number of updated assets reloaded before import = 0
Start importing Assets/tractor ui/changis/ACCPET.png using Guid(e8381f1005a773d43954b940d850d6fe) Importer(815301076,1909f56bfc062723c751e8b465ee728b)  -> (artifact id: 'deaded05bc808f38bc2b38ccb12207d3') in 0.156779 seconds 
Number of asset objects unloaded after import = 3
========================================================================
Received Import Request.
  Time since last request: 0.000062 seconds.
  path: Assets/tractor ui/SLICE (5)/SLICE/GAME PLAY/New folder/ACTIVE.png
  artifactKey: Guid(87c2babef184b644ca5c087df6c26def) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Number of updated assets reloaded before import = 0
Start importing Assets/tractor ui/SLICE (5)/SLICE/GAME PLAY/New folder/ACTIVE.png using Guid(87c2babef184b644ca5c087df6c26def) Importer(815301076,1909f56bfc062723c751e8b465ee728b)  -> (artifact id: 'c89acd8dae425ffc655ec883d6044d44') in 0.139111 seconds 
Number of asset objects unloaded after import = 3
========================================================================
Received Import Request.
  Time since last request: 0.709049 seconds.
  path: Assets/tractor ui/SLICE (5)/SLICE/MODES/ADS PAGE_.png
  artifactKey: Guid(45cb491b35308bc44a012eeef02815ae) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Number of updated assets reloaded before import = 0
Start importing Assets/tractor ui/SLICE (5)/SLICE/MODES/ADS PAGE_.png using Guid(45cb491b35308bc44a012eeef02815ae) Importer(815301076,1909f56bfc062723c751e8b465ee728b)  -> (artifact id: '864536480075128843fca456ff1b3bb3') in 0.410453 seconds 
Number of asset objects unloaded after import = 3
========================================================================
Received Import Request.
  Time since last request: 4.981387 seconds.
  path: Assets/tractor ui/SLICE (5)/SLICE/LOADING SECREEN/BACK BAR.png
  artifactKey: Guid(efc34d41291db6b47bca28eb6d42d410) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Number of updated assets reloaded before import = 0
Start importing Assets/tractor ui/SLICE (5)/SLICE/LOADING SECREEN/BACK BAR.png using Guid(efc34d41291db6b47bca28eb6d42d410) Importer(815301076,1909f56bfc062723c751e8b465ee728b)  -> (artifact id: 'ca726262f77c8abe4048915ab4d50320') in 0.341469 seconds 
Number of asset objects unloaded after import = 3
========================================================================
Received Import Request.
  Time since last request: 0.000024 seconds.
  path: Assets/Scenes/kachra/SLICING 2/INSTRUCTION/BG copy 2.png
  artifactKey: Guid(7d495fa2eac95874fb09ce3f88b79467) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Number of updated assets reloaded before import = 0
Start importing Assets/Scenes/kachra/SLICING 2/INSTRUCTION/BG copy 2.png using Guid(7d495fa2eac95874fb09ce3f88b79467) Importer(815301076,1909f56bfc062723c751e8b465ee728b)  -> (artifact id: '09861d18dc52642b7cb3f1464fbdb1cf') in 0.786067 seconds 
Number of asset objects unloaded after import = 3
========================================================================
Received Import Request.
  Time since last request: 0.000062 seconds.
  path: Assets/tractor ui/SLICE (5)/SLICE/LOADING SECREEN/BG copy 4.png
  artifactKey: Guid(e1f9f62c3177cd5408e5f6db0e9f752d) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Number of updated assets reloaded before import = 0
Start importing Assets/tractor ui/SLICE (5)/SLICE/LOADING SECREEN/BG copy 4.png using Guid(e1f9f62c3177cd5408e5f6db0e9f752d) Importer(815301076,1909f56bfc062723c751e8b465ee728b)  -> (artifact id: 'bc285017cf0e41431643fb39b8b2d866') in 0.132266 seconds 
Number of asset objects unloaded after import = 3
========================================================================
Received Import Request.
  Time since last request: 0.000023 seconds.
  path: Assets/tractor ui/SLICE (5)/SLICE/INSTRUCTION PANAL/BACK PANAL.png
  artifactKey: Guid(3889d0a66741c6f4db81fe351b840df4) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Number of updated assets reloaded before import = 0
Start importing Assets/tractor ui/SLICE (5)/SLICE/INSTRUCTION PANAL/BACK PANAL.png using Guid(3889d0a66741c6f4db81fe351b840df4) Importer(815301076,1909f56bfc062723c751e8b465ee728b)  -> (artifact id: 'd48782fb2365649c25cf97c4d25bb92e') in 0.287226 seconds 
Number of asset objects unloaded after import = 3
========================================================================
Received Import Request.
  Time since last request: 22.644470 seconds.
  path: Assets/tractor ui/changis/breaak.png
  artifactKey: Guid(0416025c6fd88c44abcc01bd449e6e4c) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Number of updated assets reloaded before import = 0
Start importing Assets/tractor ui/changis/breaak.png using Guid(0416025c6fd88c44abcc01bd449e6e4c) Importer(815301076,1909f56bfc062723c751e8b465ee728b)  -> (artifact id: 'd6a13ff9ba488098efa382f85a8cdc05') in 0.388435 seconds 
Number of asset objects unloaded after import = 3
========================================================================
Received Import Request.
  Time since last request: 0.000024 seconds.
  path: Assets/tractor ui/SLICE (5)/SLICE/MODES/career mode.png
  artifactKey: Guid(eced391ffd69e2b4e9add04fb5e07038) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Number of updated assets reloaded before import = 0
Start importing Assets/tractor ui/SLICE (5)/SLICE/MODES/career mode.png using Guid(eced391ffd69e2b4e9add04fb5e07038) Importer(815301076,1909f56bfc062723c751e8b465ee728b)  -> (artifact id: 'ab03257ed0d08f83cbea6d55acac47d1') in 0.284826 seconds 
Number of asset objects unloaded after import = 3
========================================================================
Received Import Request.
  Time since last request: 0.000061 seconds.
  path: Assets/tractor ui/SLICE (5)/SLICE/GAME PLAY/CAMERA_.png
  artifactKey: Guid(6652c25476ce5b84caff67d59645b60f) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Number of updated assets reloaded before import = 0
Start importing Assets/tractor ui/SLICE (5)/SLICE/GAME PLAY/CAMERA_.png using Guid(6652c25476ce5b84caff67d59645b60f) Importer(815301076,1909f56bfc062723c751e8b465ee728b)  -> (artifact id: '1ee7b693c6010fcf5b57bca9edb0f0c9') in 0.050271 seconds 
Number of asset objects unloaded after import = 3
========================================================================
Received Import Request.
  Time since last request: 0.000100 seconds.
  path: Assets/Scenes/kachra/SLICING 2/MAIN MENU/coin bar_.png
  artifactKey: Guid(786093122ac51314e8d333984ce8b307) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Number of updated assets reloaded before import = 0
Start importing Assets/Scenes/kachra/SLICING 2/MAIN MENU/coin bar_.png using Guid(786093122ac51314e8d333984ce8b307) Importer(815301076,1909f56bfc062723c751e8b465ee728b)  -> (artifact id: 'ca5ac184e6e6f7525c4a96ebd855a5b7') in 0.113897 seconds 
Number of asset objects unloaded after import = 3
========================================================================
Received Import Request.
  Time since last request: 0.241950 seconds.
  path: Assets/tractor ui/SLICE (5)/SLICE/GAME PLAY/control change.png
  artifactKey: Guid(383be2b6af0b8fe4594498b7fdb2dfb3) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Number of updated assets reloaded before import = 0
Start importing Assets/tractor ui/SLICE (5)/SLICE/GAME PLAY/control change.png using Guid(383be2b6af0b8fe4594498b7fdb2dfb3) Importer(815301076,1909f56bfc062723c751e8b465ee728b)  -> (artifact id: 'f329bac4323c7f970371e0b4d942e1cb') in 0.551501 seconds 
Number of asset objects unloaded after import = 3
========================================================================
Received Import Request.
  Time since last request: 0.000027 seconds.
  path: Assets/tractor ui/changis/D GLOE.png
  artifactKey: Guid(6ce4354e48137e0458ed5e7db04748e8) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Number of updated assets reloaded before import = 0
Start importing Assets/tractor ui/changis/D GLOE.png using Guid(6ce4354e48137e0458ed5e7db04748e8) Importer(815301076,1909f56bfc062723c751e8b465ee728b)  -> (artifact id: 'fd5e3188a65a86eb5395c42871ba064d') in 0.047455 seconds 
Number of asset objects unloaded after import = 3
========================================================================
Received Import Request.
  Time since last request: 0.000048 seconds.
  path: Assets/tractor ui/SLICE (5)/SLICE/GAME PLAY/New folder/D.png
  artifactKey: Guid(574b8b4b6edd8ac4c9e47d18201b9206) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Number of updated assets reloaded before import = 0
Start importing Assets/tractor ui/SLICE (5)/SLICE/GAME PLAY/New folder/D.png using Guid(574b8b4b6edd8ac4c9e47d18201b9206) Importer(815301076,1909f56bfc062723c751e8b465ee728b)  -> (artifact id: '788da4fc199b77cae3faf800b0790fef') in 0.092485 seconds 
Number of asset objects unloaded after import = 3
========================================================================
Received Import Request.
  Time since last request: 0.000027 seconds.
  path: Assets/3RDMODEASSET/download (4).png
  artifactKey: Guid(b0b2d97ae6147d5409f8981d7166ebba) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Number of updated assets reloaded before import = 0
Start importing Assets/3RDMODEASSET/download (4).png using Guid(b0b2d97ae6147d5409f8981d7166ebba) Importer(815301076,1909f56bfc062723c751e8b465ee728b)  -> (artifact id: 'a8324724b886d17c4c56438d7d4cc931') in 0.074847 seconds 
Number of asset objects unloaded after import = 3
========================================================================
Received Import Request.
  Time since last request: 0.767675 seconds.
  path: Assets/Scenes/kachra/SLICING 2/MAIN MENU/EXIT_.png
  artifactKey: Guid(651554d7d7d69474abfd7edd9054993a) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Number of updated assets reloaded before import = 0
Start importing Assets/Scenes/kachra/SLICING 2/MAIN MENU/EXIT_.png using Guid(651554d7d7d69474abfd7edd9054993a) Importer(815301076,1909f56bfc062723c751e8b465ee728b)  -> (artifact id: 'b913d8d6b74b13da5b711e08a608b736') in 0.291074 seconds 
Number of asset objects unloaded after import = 3
========================================================================
Received Import Request.
  Time since last request: 5.254781 seconds.
  path: Assets/tractor ui/SLICE (5)/SLICE/GAME PLAY/Group 12.png
  artifactKey: Guid(87032735840e97649a279be6fcef3187) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Number of updated assets reloaded before import = 0
Start importing Assets/tractor ui/SLICE (5)/SLICE/GAME PLAY/Group 12.png using Guid(87032735840e97649a279be6fcef3187) Importer(815301076,1909f56bfc062723c751e8b465ee728b)  -> (artifact id: 'd92cab15f9f849cc6f79d9aeb05b6fb4') in 0.165759 seconds 
Number of asset objects unloaded after import = 3
========================================================================
Received Import Request.
  Time since last request: 0.000045 seconds.
  path: Assets/tractor ui/SLICE (5)/SLICE/EXIT/Group 15.png
  artifactKey: Guid(5383e547e99eae2499a7515f06ba096e) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Number of updated assets reloaded before import = 0
Start importing Assets/tractor ui/SLICE (5)/SLICE/EXIT/Group 15.png using Guid(5383e547e99eae2499a7515f06ba096e) Importer(815301076,1909f56bfc062723c751e8b465ee728b)  -> (artifact id: '6d37a25674bc83188d0da8045b4ddb08') in 0.159809 seconds 
Number of asset objects unloaded after import = 3
========================================================================
Received Import Request.
  Time since last request: 1.283651 seconds.
  path: Assets/RealisticCarControllerV3/Textures/Dashboard/HazardLights.png
  artifactKey: Guid(be6516ee7f066d3479b625f021f259f9) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Number of updated assets reloaded before import = 0
Start importing Assets/RealisticCarControllerV3/Textures/Dashboard/HazardLights.png using Guid(be6516ee7f066d3479b625f021f259f9) Importer(815301076,1909f56bfc062723c751e8b465ee728b)  -> (artifact id: 'fbc3ead39548a388afdf528b50ed9077') in 0.024228 seconds 
Number of asset objects unloaded after import = 3
========================================================================
Received Import Request.
  Time since last request: 0.000024 seconds.
  path: Assets/tractor ui/SLICE (5)/SLICE/PAUSE/HOME_.png
  artifactKey: Guid(540415b623596ad4fb1765742acd7c28) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Number of updated assets reloaded before import = 0
Start importing Assets/tractor ui/SLICE (5)/SLICE/PAUSE/HOME_.png using Guid(540415b623596ad4fb1765742acd7c28) Importer(815301076,1909f56bfc062723c751e8b465ee728b)  -> (artifact id: '5206abd55bf04879fcf3d99c08ad9ab9') in 0.106219 seconds 
Number of asset objects unloaded after import = 3
========================================================================
Received Import Request.
  Time since last request: 3.149353 seconds.
  path: Assets/texture/HTB16fMVck9E3KVjSZFGq6A19XXaN-removebg-preview.png
  artifactKey: Guid(c3078653868be854f847f5bbe3dc1108) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Number of updated assets reloaded before import = 0
Start importing Assets/texture/HTB16fMVck9E3KVjSZFGq6A19XXaN-removebg-preview.png using Guid(c3078653868be854f847f5bbe3dc1108) Importer(815301076,1909f56bfc062723c751e8b465ee728b)  -> (artifact id: 'defc90e23c3e96bc91ac15fb136595d9') in 0.125604 seconds 
Number of asset objects unloaded after import = 3
========================================================================
Received Import Request.
  Time since last request: 0.000020 seconds.
  path: Assets/3RDMODEASSET/Indian-Flag-Png-Free-.png
  artifactKey: Guid(2de367a3e9709374da928a3a513f1cde) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Number of updated assets reloaded before import = 0
Start importing Assets/3RDMODEASSET/Indian-Flag-Png-Free-.png using Guid(2de367a3e9709374da928a3a513f1cde) Importer(815301076,1909f56bfc062723c751e8b465ee728b)  -> (artifact id: '3e6c05bf74a24a27b88cb0656bdc8754') in 0.263211 seconds 
Number of asset objects unloaded after import = 3
========================================================================
Received Import Request.
  Time since last request: 3.575879 seconds.
  path: Assets/RealisticCarControllerV3/Textures/Mobile Textures/JoystickHandle.png
  artifactKey: Guid(cfedd546bbecccf4582b5ad7aead5800) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Number of updated assets reloaded before import = 0
Start importing Assets/RealisticCarControllerV3/Textures/Mobile Textures/JoystickHandle.png using Guid(cfedd546bbecccf4582b5ad7aead5800) Importer(815301076,1909f56bfc062723c751e8b465ee728b)  -> (artifact id: '6f45baf8ee46b82cdb1eae89defdb863') in 0.114627 seconds 
Number of asset objects unloaded after import = 3
========================================================================
Received Import Request.
  Time since last request: 0.000025 seconds.
  path: Assets/NEWTEXTURE/Layer 12.png
  artifactKey: Guid(41eb2bba8ab22254f8092d80c5800e61) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Number of updated assets reloaded before import = 0
Start importing Assets/NEWTEXTURE/Layer 12.png using Guid(41eb2bba8ab22254f8092d80c5800e61) Importer(815301076,1909f56bfc062723c751e8b465ee728b)  -> (artifact id: '65b892ec2a0dfdf028a82c1f4183413a') in 0.123680 seconds 
Number of asset objects unloaded after import = 3
========================================================================
Received Import Request.
  Time since last request: 0.000036 seconds.
  path: Assets/tractor ui/SLICE (5)/SLICE/MAIN/Layer 11.png
  artifactKey: Guid(bf614620d8ee3b142974af4470ef7539) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Number of updated assets reloaded before import = 0
Start importing Assets/tractor ui/SLICE (5)/SLICE/MAIN/Layer 11.png using Guid(bf614620d8ee3b142974af4470ef7539) Importer(815301076,1909f56bfc062723c751e8b465ee728b)  -> (artifact id: '79e5d77147c4f3337c47765a88719ac8') in 0.036315 seconds 
Number of asset objects unloaded after import = 3
========================================================================
Received Import Request.
  Time since last request: 4.943443 seconds.
  path: Assets/tractor ui/SLICE (5)/SLICE/SETTING/Layer 679.png
  artifactKey: Guid(f26b48138362bfd4e8c603348cc172ce) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Number of updated assets reloaded before import = 0
Start importing Assets/tractor ui/SLICE (5)/SLICE/SETTING/Layer 679.png using Guid(f26b48138362bfd4e8c603348cc172ce) Importer(815301076,1909f56bfc062723c751e8b465ee728b)  -> (artifact id: 'd4cdf3e9eeb1f1d6430d374461d7a27a') in 0.234479 seconds 
Number of asset objects unloaded after import = 3
========================================================================
Received Import Request.
  Time since last request: 0.000024 seconds.
  path: Assets/Scenes/kachra/SLICING 2/MODE/Layer 803.png
  artifactKey: Guid(1c8a6f7434ade954a8cc7d998964e266) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Number of updated assets reloaded before import = 0
Start importing Assets/Scenes/kachra/SLICING 2/MODE/Layer 803.png using Guid(1c8a6f7434ade954a8cc7d998964e266) Importer(815301076,1909f56bfc062723c751e8b465ee728b)  -> (artifact id: '4dd5d516f176c3bcb9751c1e0dcb5f2c') in 0.119215 seconds 
Number of asset objects unloaded after import = 3
========================================================================
Received Import Request.
  Time since last request: 0.000035 seconds.
  path: Assets/Scenes/kachra/SLICING 2/MODE/Layer 805 copy.png
  artifactKey: Guid(a1ff27f3527c70d4eb2046656dcc87be) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Number of updated assets reloaded before import = 0
Start importing Assets/Scenes/kachra/SLICING 2/MODE/Layer 805 copy.png using Guid(a1ff27f3527c70d4eb2046656dcc87be) Importer(815301076,1909f56bfc062723c751e8b465ee728b)  -> (artifact id: '357457fe2e829b2520ae0d42fea6cae3') in 0.204079 seconds 
Number of asset objects unloaded after import = 3
========================================================================
Received Import Request.
  Time since last request: 0.000021 seconds.
  path: Assets/tractor ui/SLICE (5)/SLICE/GAME PLAY/Layer 885 copy 3.png
  artifactKey: Guid(a87a126081946004b903e4ee5a3cbe03) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Number of updated assets reloaded before import = 0
Start importing Assets/tractor ui/SLICE (5)/SLICE/GAME PLAY/Layer 885 copy 3.png using Guid(a87a126081946004b903e4ee5a3cbe03) Importer(815301076,1909f56bfc062723c751e8b465ee728b)  -> (artifact id: '89027524a347a624154ac6591eb58d58') in 0.221561 seconds 
Number of asset objects unloaded after import = 3
========================================================================
Received Import Request.
  Time since last request: 8.120869 seconds.
  path: Assets/tractor ui/SLICE (5)/SLICE/LOADING SECREEN/Layer 1097.png
  artifactKey: Guid(1ae0af580c1bf55449a5ba5e98de0cee) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Number of updated assets reloaded before import = 0
Start importing Assets/tractor ui/SLICE (5)/SLICE/LOADING SECREEN/Layer 1097.png using Guid(1ae0af580c1bf55449a5ba5e98de0cee) Importer(815301076,1909f56bfc062723c751e8b465ee728b)  -> (artifact id: '2709cb17d3df1231050339600ebdfc86') in 0.184515 seconds 
Number of asset objects unloaded after import = 3
========================================================================
Received Import Request.
  Time since last request: 0.000022 seconds.
  path: Assets/tractor ui/SLICE (5)/SLICE/LEVELS/LEVEL 05.png
  artifactKey: Guid(02b58f38b844e07499c5868ebbe82dba) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Number of updated assets reloaded before import = 0
Start importing Assets/tractor ui/SLICE (5)/SLICE/LEVELS/LEVEL 05.png using Guid(02b58f38b844e07499c5868ebbe82dba) Importer(815301076,1909f56bfc062723c751e8b465ee728b)  -> (artifact id: 'a6172ed51963c26f0681ceafb4f61cce') in 0.169469 seconds 
Number of asset objects unloaded after import = 3
========================================================================
Received Import Request.
  Time since last request: 0.000047 seconds.
  path: Assets/tractor ui/SLICE (5)/SLICE/LEVELS/LEVEL 02.png
  artifactKey: Guid(d249dead9fb937846a89486efa19bd9c) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Number of updated assets reloaded before import = 0
Start importing Assets/tractor ui/SLICE (5)/SLICE/LEVELS/LEVEL 02.png using Guid(d249dead9fb937846a89486efa19bd9c) Importer(815301076,1909f56bfc062723c751e8b465ee728b)  -> (artifact id: '6de449c9b788f4db796e6ee090601850') in 0.298814 seconds 
Number of asset objects unloaded after import = 3
Editor requested this worker to shutdown with reason: Scaling down because of idle timeout
AssetImportWorker is now disconnected from the server
Process exiting
Exiting without the bug reporter. Application will terminate with return code 0