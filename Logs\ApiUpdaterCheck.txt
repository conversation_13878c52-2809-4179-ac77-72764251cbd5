[api-updater (non-obsolete-error-filter)] 4/11/2023 4:07:36 PM : Starting C:/Program Files/Unity/Hub/Editor/2020.3.32f1/Editor/Data/Tools/ScriptUpdater/APIUpdater.NonObsoleteApiUpdaterDetector.exe
[api-updater (non-obsolete-error-filter)] 
----------------------------------
jit/startup time      : 951.1807ms
moved types parse time: 57ms
candidates parse time : 3ms
C# parse time         : 261ms
candidates check time : 29ms
console write time    : 0ms

[api-updater (non-obsolete-error-filter)] 4/11/2023 4:12:44 PM : Starting C:/Program Files/Unity/Hub/Editor/2020.3.32f1/Editor/Data/Tools/ScriptUpdater/APIUpdater.NonObsoleteApiUpdaterDetector.exe
[api-updater (non-obsolete-error-filter)] 
----------------------------------
jit/startup time      : 88.1452ms
moved types parse time: 52ms
candidates parse time : 1ms
C# parse time         : 170ms
candidates check time : 28ms
console write time    : 0ms

[api-updater (non-obsolete-error-filter)] 4/19/2023 1:38:03 PM : Starting C:/Program Files/Unity/Hub/Editor/2020.3.32f1/Editor/Data/Tools/ScriptUpdater/APIUpdater.NonObsoleteApiUpdaterDetector.exe
[api-updater (non-obsolete-error-filter)] 
----------------------------------
jit/startup time      : 1164.1454ms
moved types parse time: 61ms
candidates parse time : 1ms
C# parse time         : 305ms
candidates check time : 33ms
console write time    : 0ms

[api-updater (non-obsolete-error-filter)] 4/26/2023 11:53:42 AM : Starting C:/Program Files/Unity/Hub/Editor/2020.3.32f1/Editor/Data/Tools/ScriptUpdater/APIUpdater.NonObsoleteApiUpdaterDetector.exe
[api-updater (non-obsolete-error-filter)] 
----------------------------------
jit/startup time      : 1153.627ms
moved types parse time: 64ms
candidates parse time : 1ms
C# parse time         : 303ms
candidates check time : 36ms
console write time    : 0ms

[api-updater (non-obsolete-error-filter)] 4/27/2023 11:54:50 AM : Starting C:/Program Files/Unity/Hub/Editor/2020.3.32f1/Editor/Data/Tools/ScriptUpdater/APIUpdater.NonObsoleteApiUpdaterDetector.exe
[api-updater (non-obsolete-error-filter)] 
----------------------------------
jit/startup time      : 987.7291ms
moved types parse time: 60ms
candidates parse time : 1ms
C# parse time         : 267ms
candidates check time : 46ms
console write time    : 1ms

[api-updater (non-obsolete-error-filter)] 4/27/2023 12:25:54 PM : Starting C:/Program Files/Unity/Hub/Editor/2020.3.32f1/Editor/Data/Tools/ScriptUpdater/APIUpdater.NonObsoleteApiUpdaterDetector.exe
[api-updater (non-obsolete-error-filter)] 
----------------------------------
jit/startup time      : 98.9225ms
moved types parse time: 59ms
candidates parse time : 13ms
C# parse time         : 244ms
candidates check time : 33ms
console write time    : 1ms

[api-updater (non-obsolete-error-filter)] 4/27/2023 4:53:24 PM : Starting C:/Program Files/Unity/Hub/Editor/2020.3.32f1/Editor/Data/Tools/ScriptUpdater/APIUpdater.NonObsoleteApiUpdaterDetector.exe
[api-updater (non-obsolete-error-filter)] 
----------------------------------
jit/startup time      : 983.5267ms
moved types parse time: 55ms
candidates parse time : 1ms
C# parse time         : 366ms
candidates check time : 33ms
console write time    : 1ms

[api-updater (non-obsolete-error-filter)] 4/28/2023 12:23:50 PM : Starting C:/Program Files/Unity/Hub/Editor/2020.3.32f1/Editor/Data/Tools/ScriptUpdater/APIUpdater.NonObsoleteApiUpdaterDetector.exe
[api-updater (non-obsolete-error-filter)] 
----------------------------------
jit/startup time      : 1029.6235ms
moved types parse time: 57ms
candidates parse time : 3ms
C# parse time         : 394ms
candidates check time : 293ms
console write time    : 0ms

[api-updater (non-obsolete-error-filter)] 4/29/2023 9:07:14 AM : Starting C:/Program Files/Unity/Hub/Editor/2020.3.32f1/Editor/Data/Tools/ScriptUpdater/APIUpdater.NonObsoleteApiUpdaterDetector.exe
[api-updater (non-obsolete-error-filter)] 
----------------------------------
jit/startup time      : 1261.696ms
moved types parse time: 60ms
candidates parse time : 1ms
C# parse time         : 434ms
candidates check time : 49ms
console write time    : 1ms

[api-updater (non-obsolete-error-filter)] 4/29/2023 10:11:06 AM : Starting C:/Program Files/Unity/Hub/Editor/2020.3.32f1/Editor/Data/Tools/ScriptUpdater/APIUpdater.NonObsoleteApiUpdaterDetector.exe
[api-updater (non-obsolete-error-filter)] 
----------------------------------
jit/startup time      : 106.4617ms
moved types parse time: 57ms
candidates parse time : 3ms
C# parse time         : 209ms
candidates check time : 32ms
console write time    : 1ms

[api-updater (non-obsolete-error-filter)] 4/29/2023 10:11:23 AM : Starting C:/Program Files/Unity/Hub/Editor/2020.3.32f1/Editor/Data/Tools/ScriptUpdater/APIUpdater.NonObsoleteApiUpdaterDetector.exe
[api-updater (non-obsolete-error-filter)] 
----------------------------------
jit/startup time      : 79.1231ms
moved types parse time: 52ms
candidates parse time : 1ms
C# parse time         : 200ms
candidates check time : 47ms
console write time    : 0ms

[api-updater (non-obsolete-error-filter)] 5/3/2023 10:44:27 AM : Starting C:/Program Files/Unity/Hub/Editor/2020.3.32f1/Editor/Data/Tools/ScriptUpdater/APIUpdater.NonObsoleteApiUpdaterDetector.exe
[api-updater (non-obsolete-error-filter)] 
----------------------------------
jit/startup time      : 1178.5961ms
moved types parse time: 62ms
candidates parse time : 1ms
C# parse time         : 445ms
candidates check time : 46ms
console write time    : 0ms

[api-updater (non-obsolete-error-filter)] 5/5/2023 3:40:14 PM : Starting C:/Program Files/Unity/Hub/Editor/2020.3.32f1/Editor/Data/Tools/ScriptUpdater/APIUpdater.NonObsoleteApiUpdaterDetector.exe
[api-updater (non-obsolete-error-filter)] 
----------------------------------
jit/startup time      : 1123.2633ms
moved types parse time: 56ms
candidates parse time : 1ms
C# parse time         : 361ms
candidates check time : 39ms
console write time    : 1ms

[api-updater (non-obsolete-error-filter)] 5/8/2023 8:23:54 AM : Starting C:/Program Files/Unity/Hub/Editor/2020.3.32f1/Editor/Data/Tools/ScriptUpdater/APIUpdater.NonObsoleteApiUpdaterDetector.exe
[api-updater (non-obsolete-error-filter)] 
----------------------------------
jit/startup time      : 1022.1643ms
moved types parse time: 59ms
candidates parse time : 3ms
C# parse time         : 302ms
candidates check time : 62ms
console write time    : 0ms

[api-updater (non-obsolete-error-filter)] 5/8/2023 10:44:41 AM : Starting C:/Program Files/Unity/Hub/Editor/2020.3.32f1/Editor/Data/Tools/ScriptUpdater/APIUpdater.NonObsoleteApiUpdaterDetector.exe
[api-updater (non-obsolete-error-filter)] 
----------------------------------
jit/startup time      : 965.1189ms
moved types parse time: 69ms
candidates parse time : 12ms
C# parse time         : 313ms
candidates check time : 31ms
console write time    : 0ms

[api-updater (non-obsolete-error-filter)] 5/8/2023 11:22:12 AM : Starting C:/Program Files/Unity/Hub/Editor/2020.3.32f1/Editor/Data/Tools/ScriptUpdater/APIUpdater.NonObsoleteApiUpdaterDetector.exe
[api-updater (non-obsolete-error-filter)] 
----------------------------------
jit/startup time      : 99.9824ms
moved types parse time: 57ms
candidates parse time : 3ms
C# parse time         : 221ms
candidates check time : 33ms
console write time    : 1ms

[api-updater (non-obsolete-error-filter)] 5/9/2023 2:09:12 PM : Starting C:/Program Files/Unity/Hub/Editor/2020.3.32f1/Editor/Data/Tools/ScriptUpdater/APIUpdater.NonObsoleteApiUpdaterDetector.exe
[api-updater (non-obsolete-error-filter)] 
----------------------------------
jit/startup time      : 1008.2475ms
moved types parse time: 56ms
candidates parse time : 1ms
C# parse time         : 280ms
candidates check time : 34ms
console write time    : 0ms

[api-updater (non-obsolete-error-filter)] 9/15/2023 6:39:42 PM : Starting C:/Program Files/Unity/Hub/Editor/2020.3.32f1/Editor/Data/Tools/ScriptUpdater/APIUpdater.NonObsoleteApiUpdaterDetector.exe
[api-updater (non-obsolete-error-filter)] Exception caught while loading types from D:\cargo truck\Tractor Simulator Cargo Games (V3)smg\Library\ScriptAssemblies\Unity.TextMeshPro.Editor.dll (some types may not be loaded)
	Exception of type 'System.Reflection.ReflectionTypeLoadException' was thrown.
	  at (wrapper managed-to-native) System.Reflection.Assembly.GetTypes(System.Reflection.Assembly,bool)
  at System.Reflection.Assembly.GetTypes () [0x00000] in <695d1cc93cca45069c528c15c9fdd749>:0 
  at APIUpdater.NonObsoleteApiUpdaterDetector.ExtraInfoParser+<LoadTypesWithMovedFromAttributeAsync>d__3.MoveNext () [0x000c8] in <68bff7873e0e4aa69a14dfc30bebbe3e>:0 
	Could not load file or assembly 'UnityEditor.CoreModule, Version=0.0.0.0, Culture=neutral, PublicKeyToken=null' or one of its dependencies.

[api-updater (non-obsolete-error-filter)] 
----------------------------------
jit/startup time      : 183.8286ms
moved types parse time: 82ms
candidates parse time : 1ms
C# parse time         : 305ms
candidates check time : 76ms
console write time    : 0ms

[api-updater (non-obsolete-error-filter)] 9/15/2023 6:39:50 PM : Starting C:/Program Files/Unity/Hub/Editor/2020.3.32f1/Editor/Data/Tools/ScriptUpdater/APIUpdater.NonObsoleteApiUpdaterDetector.exe
[api-updater (non-obsolete-error-filter)] Exception caught while loading types from D:\cargo truck\Tractor Simulator Cargo Games (V3)smg\Library\ScriptAssemblies\Unity.TextMeshPro.Editor.dll (some types may not be loaded)
	Exception of type 'System.Reflection.ReflectionTypeLoadException' was thrown.
	  at (wrapper managed-to-native) System.Reflection.Assembly.GetTypes(System.Reflection.Assembly,bool)
  at System.Reflection.Assembly.GetTypes () [0x00000] in <695d1cc93cca45069c528c15c9fdd749>:0 
  at APIUpdater.NonObsoleteApiUpdaterDetector.ExtraInfoParser+<LoadTypesWithMovedFromAttributeAsync>d__3.MoveNext () [0x000c8] in <68bff7873e0e4aa69a14dfc30bebbe3e>:0 
	Could not load file or assembly 'UnityEditor.CoreModule, Version=0.0.0.0, Culture=neutral, PublicKeyToken=null' or one of its dependencies.

[api-updater (non-obsolete-error-filter)] 
----------------------------------
jit/startup time      : 75.9773ms
moved types parse time: 52ms
candidates parse time : 1ms
C# parse time         : 186ms
candidates check time : 64ms
console write time    : 0ms

[api-updater (non-obsolete-error-filter)] 9/19/2023 9:32:57 AM : Starting C:/Program Files/Unity/Hub/Editor/2020.3.32f1/Editor/Data/Tools/ScriptUpdater/APIUpdater.NonObsoleteApiUpdaterDetector.exe
[api-updater (non-obsolete-error-filter)] 
----------------------------------
jit/startup time      : 1242.2314ms
moved types parse time: 49ms
candidates parse time : 1ms
C# parse time         : 296ms
candidates check time : 58ms
console write time    : 1ms

[api-updater (non-obsolete-error-filter)] 9/19/2023 9:33:26 AM : Starting C:/Program Files/Unity/Hub/Editor/2020.3.32f1/Editor/Data/Tools/ScriptUpdater/APIUpdater.NonObsoleteApiUpdaterDetector.exe
[api-updater (non-obsolete-error-filter)] 
----------------------------------
jit/startup time      : 79.9467ms
moved types parse time: 51ms
candidates parse time : 1ms
C# parse time         : 210ms
candidates check time : 46ms
console write time    : 0ms

[api-updater (non-obsolete-error-filter)] 9/19/2023 9:33:32 AM : Starting C:/Program Files/Unity/Hub/Editor/2020.3.32f1/Editor/Data/Tools/ScriptUpdater/APIUpdater.NonObsoleteApiUpdaterDetector.exe
[api-updater (non-obsolete-error-filter)] 
----------------------------------
jit/startup time      : 62.9655ms
moved types parse time: 50ms
candidates parse time : 1ms
C# parse time         : 196ms
candidates check time : 46ms
console write time    : 0ms

[api-updater (non-obsolete-error-filter)] 9/19/2023 9:33:40 AM : Starting C:/Program Files/Unity/Hub/Editor/2020.3.32f1/Editor/Data/Tools/ScriptUpdater/APIUpdater.NonObsoleteApiUpdaterDetector.exe
[api-updater (non-obsolete-error-filter)] 
----------------------------------
jit/startup time      : 98.941ms
moved types parse time: 101ms
candidates parse time : 1ms
C# parse time         : 284ms
candidates check time : 47ms
console write time    : 0ms

[api-updater (non-obsolete-error-filter)] 9/19/2023 9:34:14 AM : Starting C:/Program Files/Unity/Hub/Editor/2020.3.32f1/Editor/Data/Tools/ScriptUpdater/APIUpdater.NonObsoleteApiUpdaterDetector.exe
[api-updater (non-obsolete-error-filter)] 
----------------------------------
jit/startup time      : 79.9545ms
moved types parse time: 59ms
candidates parse time : 3ms
C# parse time         : 232ms
candidates check time : 42ms
console write time    : 0ms

[api-updater (non-obsolete-error-filter)] 9/19/2023 9:34:32 AM : Starting C:/Program Files/Unity/Hub/Editor/2020.3.32f1/Editor/Data/Tools/ScriptUpdater/APIUpdater.NonObsoleteApiUpdaterDetector.exe
[api-updater (non-obsolete-error-filter)] 
----------------------------------
jit/startup time      : 74.9572ms
moved types parse time: 60ms
candidates parse time : 1ms
C# parse time         : 260ms
candidates check time : 43ms
console write time    : 1ms

[api-updater (non-obsolete-error-filter)] 9/19/2023 9:35:26 AM : Starting C:/Program Files/Unity/Hub/Editor/2020.3.32f1/Editor/Data/Tools/ScriptUpdater/APIUpdater.NonObsoleteApiUpdaterDetector.exe
[api-updater (non-obsolete-error-filter)] 
----------------------------------
jit/startup time      : 89.9493ms
moved types parse time: 54ms
candidates parse time : 1ms
C# parse time         : 204ms
candidates check time : 52ms
console write time    : 1ms

[api-updater (non-obsolete-error-filter)] 9/19/2023 9:37:59 AM : Starting C:/Program Files/Unity/Hub/Editor/2020.3.32f1/Editor/Data/Tools/ScriptUpdater/APIUpdater.NonObsoleteApiUpdaterDetector.exe
[api-updater (non-obsolete-error-filter)] 
----------------------------------
jit/startup time      : 68.9592ms
moved types parse time: 53ms
candidates parse time : 1ms
C# parse time         : 227ms
candidates check time : 32ms
console write time    : 0ms

[api-updater (non-obsolete-error-filter)] 9/19/2023 9:43:57 AM : Starting C:/Program Files/Unity/Hub/Editor/2020.3.32f1/Editor/Data/Tools/ScriptUpdater/APIUpdater.NonObsoleteApiUpdaterDetector.exe
[api-updater (non-obsolete-error-filter)] 
----------------------------------
jit/startup time      : 77.9716ms
moved types parse time: 63ms
candidates parse time : 2ms
C# parse time         : 218ms
candidates check time : 32ms
console write time    : 0ms

[api-updater (non-obsolete-error-filter)] 9/19/2023 10:17:06 AM : Starting C:/Program Files/Unity/Hub/Editor/2020.3.32f1/Editor/Data/Tools/ScriptUpdater/APIUpdater.NonObsoleteApiUpdaterDetector.exe
[api-updater (non-obsolete-error-filter)] 
----------------------------------
jit/startup time      : 1449.5279ms
moved types parse time: 75ms
candidates parse time : 3ms
C# parse time         : 351ms
candidates check time : 70ms
console write time    : 1ms

[api-updater (non-obsolete-error-filter)] 9/19/2023 11:34:24 AM : Starting C:/Program Files/Unity/Hub/Editor/2020.3.32f1/Editor/Data/Tools/ScriptUpdater/APIUpdater.NonObsoleteApiUpdaterDetector.exe
[api-updater (non-obsolete-error-filter)] 
----------------------------------
jit/startup time      : 86.9794ms
moved types parse time: 54ms
candidates parse time : 1ms
C# parse time         : 228ms
candidates check time : 99ms
console write time    : 0ms

[api-updater (non-obsolete-error-filter)] 9/19/2023 11:34:40 AM : Starting C:/Program Files/Unity/Hub/Editor/2020.3.32f1/Editor/Data/Tools/ScriptUpdater/APIUpdater.NonObsoleteApiUpdaterDetector.exe
[api-updater (non-obsolete-error-filter)] 
----------------------------------
jit/startup time      : 101.9425ms
moved types parse time: 56ms
candidates parse time : 2ms
C# parse time         : 222ms
candidates check time : 86ms
console write time    : 0ms

[api-updater (non-obsolete-error-filter)] 9/19/2023 11:36:02 AM : Starting C:/Program Files/Unity/Hub/Editor/2020.3.32f1/Editor/Data/Tools/ScriptUpdater/APIUpdater.NonObsoleteApiUpdaterDetector.exe
[api-updater (non-obsolete-error-filter)] 
----------------------------------
jit/startup time      : 75.9547ms
moved types parse time: 61ms
candidates parse time : 2ms
C# parse time         : 194ms
candidates check time : 54ms
console write time    : 0ms

[api-updater (non-obsolete-error-filter)] 9/19/2023 11:37:41 AM : Starting C:/Program Files/Unity/Hub/Editor/2020.3.32f1/Editor/Data/Tools/ScriptUpdater/APIUpdater.NonObsoleteApiUpdaterDetector.exe
[api-updater (non-obsolete-error-filter)] 
----------------------------------
jit/startup time      : 67.9907ms
moved types parse time: 52ms
candidates parse time : 1ms
C# parse time         : 198ms
candidates check time : 51ms
console write time    : 0ms

[api-updater (non-obsolete-error-filter)] 9/19/2023 11:38:43 AM : Starting C:/Program Files/Unity/Hub/Editor/2020.3.32f1/Editor/Data/Tools/ScriptUpdater/APIUpdater.NonObsoleteApiUpdaterDetector.exe
[api-updater (non-obsolete-error-filter)] 
----------------------------------
jit/startup time      : 103.0246ms
moved types parse time: 49ms
candidates parse time : 1ms
C# parse time         : 159ms
candidates check time : 32ms
console write time    : 0ms

[api-updater (non-obsolete-error-filter)] 9/21/2023 12:45:09 PM : Starting C:/Program Files/Unity/Hub/Editor/2020.3.32f1/Editor/Data/Tools/ScriptUpdater/APIUpdater.NonObsoleteApiUpdaterDetector.exe
[api-updater (non-obsolete-error-filter)] 
----------------------------------
jit/startup time      : 1365.9999ms
moved types parse time: 58ms
candidates parse time : 1ms
C# parse time         : 301ms
candidates check time : 32ms
console write time    : 1ms

[api-updater (non-obsolete-error-filter)] 9/21/2023 6:01:08 PM : Starting C:/Program Files/Unity/Hub/Editor/2020.3.32f1/Editor/Data/Tools/ScriptUpdater/APIUpdater.NonObsoleteApiUpdaterDetector.exe
[api-updater (non-obsolete-error-filter)] 
----------------------------------
jit/startup time      : 103.9381ms
moved types parse time: 52ms
candidates parse time : 1ms
C# parse time         : 206ms
candidates check time : 38ms
console write time    : 0ms

[api-updater (non-obsolete-error-filter)] 9/21/2023 6:01:34 PM : Starting C:/Program Files/Unity/Hub/Editor/2020.3.32f1/Editor/Data/Tools/ScriptUpdater/APIUpdater.NonObsoleteApiUpdaterDetector.exe
[api-updater (non-obsolete-error-filter)] 
----------------------------------
jit/startup time      : 103.9433ms
moved types parse time: 53ms
candidates parse time : 1ms
C# parse time         : 230ms
candidates check time : 39ms
console write time    : 1ms

[api-updater (non-obsolete-error-filter)] 9/21/2023 6:01:59 PM : Starting C:/Program Files/Unity/Hub/Editor/2020.3.32f1/Editor/Data/Tools/ScriptUpdater/APIUpdater.NonObsoleteApiUpdaterDetector.exe
[api-updater (non-obsolete-error-filter)] 
----------------------------------
jit/startup time      : 109.9335ms
moved types parse time: 54ms
candidates parse time : 3ms
C# parse time         : 225ms
candidates check time : 37ms
console write time    : 1ms

[api-updater (non-obsolete-error-filter)] 9/22/2023 3:02:09 PM : Starting C:/Program Files/Unity/Hub/Editor/2020.3.32f1/Editor/Data/Tools/ScriptUpdater/APIUpdater.NonObsoleteApiUpdaterDetector.exe
[api-updater (non-obsolete-error-filter)] 
----------------------------------
jit/startup time      : 1383.2529ms
moved types parse time: 57ms
candidates parse time : 1ms
C# parse time         : 294ms
candidates check time : 30ms
console write time    : 0ms

[api-updater (non-obsolete-error-filter)] 9/22/2023 3:05:37 PM : Starting C:/Program Files/Unity/Hub/Editor/2020.3.32f1/Editor/Data/Tools/ScriptUpdater/APIUpdater.NonObsoleteApiUpdaterDetector.exe
[api-updater (non-obsolete-error-filter)] 
----------------------------------
jit/startup time      : 109.938ms
moved types parse time: 58ms
candidates parse time : 9ms
C# parse time         : 200ms
candidates check time : 29ms
console write time    : 0ms

[api-updater (non-obsolete-error-filter)] 9/22/2023 5:03:35 PM : Starting C:/Program Files/Unity/Hub/Editor/2020.3.32f1/Editor/Data/Tools/ScriptUpdater/APIUpdater.NonObsoleteApiUpdaterDetector.exe
[api-updater (non-obsolete-error-filter)] 
----------------------------------
jit/startup time      : 1202.5372ms
moved types parse time: 58ms
candidates parse time : 3ms
C# parse time         : 333ms
candidates check time : 32ms
console write time    : 1ms

[api-updater (non-obsolete-error-filter)] 9/25/2023 9:12:08 AM : Starting C:/Program Files/Unity/Hub/Editor/2020.3.32f1/Editor/Data/Tools/ScriptUpdater/APIUpdater.NonObsoleteApiUpdaterDetector.exe
[api-updater (non-obsolete-error-filter)] 
----------------------------------
jit/startup time      : 1160.6971ms
moved types parse time: 53ms
candidates parse time : 3ms
C# parse time         : 298ms
candidates check time : 39ms
console write time    : 0ms

[api-updater (non-obsolete-error-filter)] 9/25/2023 11:31:37 AM : Starting C:/Program Files/Unity/Hub/Editor/2020.3.32f1/Editor/Data/Tools/ScriptUpdater/APIUpdater.NonObsoleteApiUpdaterDetector.exe
[api-updater (non-obsolete-error-filter)] 
----------------------------------
jit/startup time      : 1274.8153ms
moved types parse time: 51ms
candidates parse time : 3ms
C# parse time         : 325ms
candidates check time : 57ms
console write time    : 1ms

[api-updater (non-obsolete-error-filter)] 9/25/2023 6:53:53 PM : Starting C:/Program Files/Unity/Hub/Editor/2020.3.32f1/Editor/Data/Tools/ScriptUpdater/APIUpdater.NonObsoleteApiUpdaterDetector.exe
[api-updater (non-obsolete-error-filter)] 
----------------------------------
jit/startup time      : 94.9762ms
moved types parse time: 58ms
candidates parse time : 1ms
C# parse time         : 242ms
candidates check time : 32ms
console write time    : 0ms

[api-updater (non-obsolete-error-filter)] 9/26/2023 11:43:26 AM : Starting C:/Program Files/Unity/Hub/Editor/2020.3.32f1/Editor/Data/Tools/ScriptUpdater/APIUpdater.NonObsoleteApiUpdaterDetector.exe
[api-updater (non-obsolete-error-filter)] 
----------------------------------
jit/startup time      : 1449.5342ms
moved types parse time: 73ms
candidates parse time : 3ms
C# parse time         : 386ms
candidates check time : 47ms
console write time    : 1ms

[api-updater (non-obsolete-error-filter)] 9/26/2023 5:07:06 PM : Starting C:/Program Files/Unity/Hub/Editor/2020.3.32f1/Editor/Data/Tools/ScriptUpdater/APIUpdater.NonObsoleteApiUpdaterDetector.exe
[api-updater (non-obsolete-error-filter)] 
----------------------------------
jit/startup time      : 284.8867ms
moved types parse time: 67ms
candidates parse time : 1ms
C# parse time         : 303ms
candidates check time : 62ms
console write time    : 1ms

[api-updater (non-obsolete-error-filter)] 9/27/2023 9:48:40 AM : Starting C:/Program Files/Unity/Hub/Editor/2020.3.32f1/Editor/Data/Tools/ScriptUpdater/APIUpdater.NonObsoleteApiUpdaterDetector.exe
[api-updater (non-obsolete-error-filter)] 
----------------------------------
jit/startup time      : 1477.5409ms
moved types parse time: 55ms
candidates parse time : 1ms
C# parse time         : 215ms
candidates check time : 31ms
console write time    : 0ms

[api-updater (non-obsolete-error-filter)] 9/27/2023 9:49:48 AM : Starting C:/Program Files/Unity/Hub/Editor/2020.3.32f1/Editor/Data/Tools/ScriptUpdater/APIUpdater.NonObsoleteApiUpdaterDetector.exe
[api-updater (non-obsolete-error-filter)] Exception caught while loading types from D:\cargo truck\Tractor Simulator Cargo Games (V3)smg22.9.2023\Library\ScriptAssemblies\Unity.TextMeshPro.Editor.dll (some types may not be loaded)
	Exception of type 'System.Reflection.ReflectionTypeLoadException' was thrown.
	  at (wrapper managed-to-native) System.Reflection.Assembly.GetTypes(System.Reflection.Assembly,bool)
  at System.Reflection.Assembly.GetTypes () [0x00000] in <695d1cc93cca45069c528c15c9fdd749>:0 
  at APIUpdater.NonObsoleteApiUpdaterDetector.ExtraInfoParser+<LoadTypesWithMovedFromAttributeAsync>d__3.MoveNext () [0x000c8] in <68bff7873e0e4aa69a14dfc30bebbe3e>:0 
	Could not load file or assembly 'UnityEditor.CoreModule, Version=0.0.0.0, Culture=neutral, PublicKeyToken=null' or one of its dependencies.

[api-updater (non-obsolete-error-filter)] 
----------------------------------
jit/startup time      : 114.1164ms
moved types parse time: 54ms
candidates parse time : 1ms
C# parse time         : 229ms
candidates check time : 31ms
console write time    : 0ms

[api-updater (non-obsolete-error-filter)] 9/27/2023 12:10:11 PM : Starting C:/Program Files/Unity/Hub/Editor/2020.3.32f1/Editor/Data/Tools/ScriptUpdater/APIUpdater.NonObsoleteApiUpdaterDetector.exe
[api-updater (non-obsolete-error-filter)] 
----------------------------------
jit/startup time      : 256.89ms
moved types parse time: 59ms
candidates parse time : 3ms
C# parse time         : 379ms
candidates check time : 37ms
console write time    : 0ms

[api-updater (non-obsolete-error-filter)] 9/27/2023 2:21:54 PM : Starting C:/Program Files/Unity/Hub/Editor/2020.3.32f1/Editor/Data/Tools/ScriptUpdater/APIUpdater.NonObsoleteApiUpdaterDetector.exe
[api-updater (non-obsolete-error-filter)] 
----------------------------------
jit/startup time      : 88.6764ms
moved types parse time: 65ms
candidates parse time : 1ms
C# parse time         : 219ms
candidates check time : 32ms
console write time    : 1ms

[api-updater (non-obsolete-error-filter)] 9/27/2023 2:22:00 PM : Starting C:/Program Files/Unity/Hub/Editor/2020.3.32f1/Editor/Data/Tools/ScriptUpdater/APIUpdater.NonObsoleteApiUpdaterDetector.exe
[api-updater (non-obsolete-error-filter)] 
----------------------------------
jit/startup time      : 111.9634ms
moved types parse time: 53ms
candidates parse time : 1ms
C# parse time         : 226ms
candidates check time : 41ms
console write time    : 0ms

[api-updater (non-obsolete-error-filter)] 9/27/2023 2:51:59 PM : Starting C:/Program Files/Unity/Hub/Editor/2020.3.32f1/Editor/Data/Tools/ScriptUpdater/APIUpdater.NonObsoleteApiUpdaterDetector.exe
[api-updater (non-obsolete-error-filter)] 
----------------------------------
jit/startup time      : 113.7384ms
moved types parse time: 57ms
candidates parse time : 2ms
C# parse time         : 210ms
candidates check time : 52ms
console write time    : 0ms

[api-updater (non-obsolete-error-filter)] 10/14/2023 4:15:15 PM : Starting C:/Program Files/Unity/Hub/Editor/2020.3.32f1/Editor/Data/Tools/ScriptUpdater/APIUpdater.NonObsoleteApiUpdaterDetector.exe
[api-updater (non-obsolete-error-filter)] 
----------------------------------
jit/startup time      : 167.9045ms
moved types parse time: 56ms
candidates parse time : 9ms
C# parse time         : 312ms
candidates check time : 51ms
console write time    : 0ms

[api-updater (non-obsolete-error-filter)] 10/14/2023 4:16:42 PM : Starting C:/Program Files/Unity/Hub/Editor/2020.3.32f1/Editor/Data/Tools/ScriptUpdater/APIUpdater.NonObsoleteApiUpdaterDetector.exe
[api-updater (non-obsolete-error-filter)] 
----------------------------------
jit/startup time      : 74.0294ms
moved types parse time: 56ms
candidates parse time : 1ms
C# parse time         : 225ms
candidates check time : 58ms
console write time    : 0ms

[api-updater (non-obsolete-error-filter)] 10/14/2023 5:18:01 PM : Starting C:/Program Files/Unity/Hub/Editor/2020.3.32f1/Editor/Data/Tools/ScriptUpdater/APIUpdater.NonObsoleteApiUpdaterDetector.exe
[api-updater (non-obsolete-error-filter)] 
----------------------------------
jit/startup time      : 84.5015ms
moved types parse time: 57ms
candidates parse time : 1ms
C# parse time         : 205ms
candidates check time : 44ms
console write time    : 0ms

[api-updater (non-obsolete-error-filter)] 10/16/2023 9:42:21 AM : Starting C:/Program Files/Unity/Hub/Editor/2020.3.32f1/Editor/Data/Tools/ScriptUpdater/APIUpdater.NonObsoleteApiUpdaterDetector.exe
[api-updater (non-obsolete-error-filter)] 
----------------------------------
jit/startup time      : 1393.5283ms
moved types parse time: 52ms
candidates parse time : 1ms
C# parse time         : 322ms
candidates check time : 54ms
console write time    : 0ms

[api-updater (non-obsolete-error-filter)] 10/16/2023 10:19:51 AM : Starting C:/Program Files/Unity/Hub/Editor/2020.3.32f1/Editor/Data/Tools/ScriptUpdater/APIUpdater.NonObsoleteApiUpdaterDetector.exe
[api-updater (non-obsolete-error-filter)] 
----------------------------------
jit/startup time      : 105.932ms
moved types parse time: 61ms
candidates parse time : 1ms
C# parse time         : 190ms
candidates check time : 49ms
console write time    : 0ms

[api-updater (non-obsolete-error-filter)] 10/16/2023 10:21:09 AM : Starting C:/Program Files/Unity/Hub/Editor/2020.3.32f1/Editor/Data/Tools/ScriptUpdater/APIUpdater.NonObsoleteApiUpdaterDetector.exe
[api-updater (non-obsolete-error-filter)] 
----------------------------------
jit/startup time      : 97.9451ms
moved types parse time: 53ms
candidates parse time : 1ms
C# parse time         : 202ms
candidates check time : 54ms
console write time    : 0ms

[api-updater (non-obsolete-error-filter)] 10/16/2023 10:29:10 AM : Starting C:/Program Files/Unity/Hub/Editor/2020.3.32f1/Editor/Data/Tools/ScriptUpdater/APIUpdater.NonObsoleteApiUpdaterDetector.exe
[api-updater (non-obsolete-error-filter)] 
----------------------------------
jit/startup time      : 76.6617ms
moved types parse time: 51ms
candidates parse time : 1ms
C# parse time         : 216ms
candidates check time : 42ms
console write time    : 0ms

[api-updater (non-obsolete-error-filter)] 10/16/2023 10:30:43 AM : Starting C:/Program Files/Unity/Hub/Editor/2020.3.32f1/Editor/Data/Tools/ScriptUpdater/APIUpdater.NonObsoleteApiUpdaterDetector.exe
[api-updater (non-obsolete-error-filter)] 
----------------------------------
jit/startup time      : 102.1711ms
moved types parse time: 56ms
candidates parse time : 12ms
C# parse time         : 196ms
candidates check time : 42ms
console write time    : 0ms

[api-updater (non-obsolete-error-filter)] 10/16/2023 12:30:59 PM : Starting C:/Program Files/Unity/Hub/Editor/2020.3.32f1/Editor/Data/Tools/ScriptUpdater/APIUpdater.NonObsoleteApiUpdaterDetector.exe
[api-updater (non-obsolete-error-filter)] 
----------------------------------
jit/startup time      : 1268.3874ms
moved types parse time: 87ms
candidates parse time : 3ms
C# parse time         : 478ms
candidates check time : 38ms
console write time    : 0ms

[api-updater (non-obsolete-error-filter)] 10/16/2023 2:37:12 PM : Starting C:/Program Files/Unity/Hub/Editor/2020.3.32f1/Editor/Data/Tools/ScriptUpdater/APIUpdater.NonObsoleteApiUpdaterDetector.exe
[api-updater (non-obsolete-error-filter)] 
----------------------------------
jit/startup time      : 111.4945ms
moved types parse time: 54ms
candidates parse time : 1ms
C# parse time         : 226ms
candidates check time : 54ms
console write time    : 0ms

[api-updater (non-obsolete-error-filter)] 10/18/2023 11:03:18 AM : Starting C:/Program Files/Unity/Hub/Editor/2020.3.32f1/Editor/Data/Tools/ScriptUpdater/APIUpdater.NonObsoleteApiUpdaterDetector.exe
[api-updater (non-obsolete-error-filter)] 
----------------------------------
jit/startup time      : 1656.3244ms
moved types parse time: 60ms
candidates parse time : 1ms
C# parse time         : 491ms
candidates check time : 48ms
console write time    : 0ms

[api-updater (non-obsolete-error-filter)] 10/18/2023 11:18:30 AM : Starting C:/Program Files/Unity/Hub/Editor/2020.3.32f1/Editor/Data/Tools/ScriptUpdater/APIUpdater.NonObsoleteApiUpdaterDetector.exe
[api-updater (non-obsolete-error-filter)] 
----------------------------------
jit/startup time      : 95.3468ms
moved types parse time: 58ms
candidates parse time : 3ms
C# parse time         : 209ms
candidates check time : 50ms
console write time    : 1ms

[api-updater (non-obsolete-error-filter)] 10/18/2023 11:18:38 AM : Starting C:/Program Files/Unity/Hub/Editor/2020.3.32f1/Editor/Data/Tools/ScriptUpdater/APIUpdater.NonObsoleteApiUpdaterDetector.exe
[api-updater (non-obsolete-error-filter)] 
----------------------------------
jit/startup time      : 108.1594ms
moved types parse time: 58ms
candidates parse time : 2ms
C# parse time         : 225ms
candidates check time : 69ms
console write time    : 1ms

[api-updater (non-obsolete-error-filter)] 10/18/2023 5:24:15 PM : Starting C:/Program Files/Unity/Hub/Editor/2020.3.32f1/Editor/Data/Tools/ScriptUpdater/APIUpdater.NonObsoleteApiUpdaterDetector.exe
[api-updater (non-obsolete-error-filter)] 
----------------------------------
jit/startup time      : 72.9575ms
moved types parse time: 53ms
candidates parse time : 1ms
C# parse time         : 237ms
candidates check time : 52ms
console write time    : 0ms

[api-updater (non-obsolete-error-filter)] 10/19/2023 3:58:45 PM : Starting C:/Program Files/Unity/Hub/Editor/2020.3.32f1/Editor/Data/Tools/ScriptUpdater/APIUpdater.NonObsoleteApiUpdaterDetector.exe
[api-updater (non-obsolete-error-filter)] 
----------------------------------
jit/startup time      : 1513.0999ms
moved types parse time: 73ms
candidates parse time : 3ms
C# parse time         : 394ms
candidates check time : 37ms
console write time    : 0ms

[api-updater (non-obsolete-error-filter)] 10/19/2023 3:58:56 PM : Starting C:/Program Files/Unity/Hub/Editor/2020.3.32f1/Editor/Data/Tools/ScriptUpdater/APIUpdater.NonObsoleteApiUpdaterDetector.exe
[api-updater (non-obsolete-error-filter)] 
----------------------------------
jit/startup time      : 98.9702ms
moved types parse time: 55ms
candidates parse time : 1ms
C# parse time         : 196ms
candidates check time : 37ms
console write time    : 0ms

[api-updater (non-obsolete-error-filter)] 11/29/2023 4:43:12 PM : Starting C:/Program Files/Unity/Hub/Editor/2020.3.32f1/Editor/Data/Tools/ScriptUpdater/APIUpdater.NonObsoleteApiUpdaterDetector.exe
[api-updater (non-obsolete-error-filter)] 
----------------------------------
jit/startup time      : 1351.9021ms
moved types parse time: 59ms
candidates parse time : 1ms
C# parse time         : 359ms
candidates check time : 77ms
console write time    : 1ms

[api-updater (non-obsolete-error-filter)] 11/29/2023 4:43:32 PM : Starting C:/Program Files/Unity/Hub/Editor/2020.3.32f1/Editor/Data/Tools/ScriptUpdater/APIUpdater.NonObsoleteApiUpdaterDetector.exe
[api-updater (non-obsolete-error-filter)] 
----------------------------------
jit/startup time      : 131.1874ms
moved types parse time: 76ms
candidates parse time : 1ms
C# parse time         : 183ms
candidates check time : 71ms
console write time    : 0ms

[api-updater (non-obsolete-error-filter)] 11/29/2023 4:44:07 PM : Starting C:/Program Files/Unity/Hub/Editor/2020.3.32f1/Editor/Data/Tools/ScriptUpdater/APIUpdater.NonObsoleteApiUpdaterDetector.exe
[api-updater (non-obsolete-error-filter)] 
----------------------------------
jit/startup time      : 69.96ms
moved types parse time: 53ms
candidates parse time : 1ms
C# parse time         : 194ms
candidates check time : 72ms
console write time    : 0ms

[api-updater (non-obsolete-error-filter)] 11/29/2023 4:47:00 PM : Starting C:/Program Files/Unity/Hub/Editor/2020.3.32f1/Editor/Data/Tools/ScriptUpdater/APIUpdater.NonObsoleteApiUpdaterDetector.exe
[api-updater (non-obsolete-error-filter)] 
----------------------------------
jit/startup time      : 79.9926ms
moved types parse time: 64ms
candidates parse time : 2ms
C# parse time         : 211ms
candidates check time : 59ms
console write time    : 0ms

[api-updater (non-obsolete-error-filter)] 11/29/2023 4:48:15 PM : Starting C:/Program Files/Unity/Hub/Editor/2020.3.32f1/Editor/Data/Tools/ScriptUpdater/APIUpdater.NonObsoleteApiUpdaterDetector.exe
[api-updater (non-obsolete-error-filter)] 
----------------------------------
jit/startup time      : 75.9564ms
moved types parse time: 62ms
candidates parse time : 1ms
C# parse time         : 198ms
candidates check time : 59ms
console write time    : 0ms

[api-updater (non-obsolete-error-filter)] 11/29/2023 4:48:42 PM : Starting C:/Program Files/Unity/Hub/Editor/2020.3.32f1/Editor/Data/Tools/ScriptUpdater/APIUpdater.NonObsoleteApiUpdaterDetector.exe
[api-updater (non-obsolete-error-filter)] 
----------------------------------
jit/startup time      : 122.9593ms
moved types parse time: 53ms
candidates parse time : 1ms
C# parse time         : 214ms
candidates check time : 31ms
console write time    : 2ms

[api-updater (non-obsolete-error-filter)] 12/1/2023 11:35:57 AM : Starting C:/Program Files/Unity/Hub/Editor/2020.3.32f1/Editor/Data/Tools/ScriptUpdater/APIUpdater.NonObsoleteApiUpdaterDetector.exe
[api-updater (non-obsolete-error-filter)] 
----------------------------------
jit/startup time      : 1132.6072ms
moved types parse time: 54ms
candidates parse time : 1ms
C# parse time         : 417ms
candidates check time : 35ms
console write time    : 1ms

[api-updater (non-obsolete-error-filter)] 1/12/2024 12:17:54 PM : Starting C:/Program Files/Unity/Hub/Editor/2020.3.32f1/Editor/Data/Tools/ScriptUpdater/APIUpdater.NonObsoleteApiUpdaterDetector.exe
[api-updater (non-obsolete-error-filter)] 
----------------------------------
jit/startup time      : 161.903ms
moved types parse time: 55ms
candidates parse time : 1ms
C# parse time         : 297ms
candidates check time : 59ms
console write time    : 0ms

[api-updater (non-obsolete-error-filter)] 1/12/2024 12:18:14 PM : Starting C:/Program Files/Unity/Hub/Editor/2020.3.32f1/Editor/Data/Tools/ScriptUpdater/APIUpdater.NonObsoleteApiUpdaterDetector.exe
[api-updater (non-obsolete-error-filter)] 
----------------------------------
jit/startup time      : 55.9678ms
moved types parse time: 51ms
candidates parse time : 1ms
C# parse time         : 169ms
candidates check time : 59ms
console write time    : 0ms

[api-updater (non-obsolete-error-filter)] 1/12/2024 12:25:24 PM : Starting C:/Program Files/Unity/Hub/Editor/2020.3.32f1/Editor/Data/Tools/ScriptUpdater/APIUpdater.NonObsoleteApiUpdaterDetector.exe
[api-updater (non-obsolete-error-filter)] 
----------------------------------
jit/startup time      : 59.9867ms
moved types parse time: 49ms
candidates parse time : 1ms
C# parse time         : 173ms
candidates check time : 57ms
console write time    : 0ms

[api-updater (non-obsolete-error-filter)] 1/12/2024 12:27:20 PM : Starting C:/Program Files/Unity/Hub/Editor/2020.3.32f1/Editor/Data/Tools/ScriptUpdater/APIUpdater.NonObsoleteApiUpdaterDetector.exe
[api-updater (non-obsolete-error-filter)] 
----------------------------------
jit/startup time      : 95.9482ms
moved types parse time: 56ms
candidates parse time : 1ms
C# parse time         : 254ms
candidates check time : 32ms
console write time    : 1ms

[api-updater (non-obsolete-error-filter)] 1/12/2024 12:27:58 PM : Starting C:/Program Files/Unity/Hub/Editor/2020.3.32f1/Editor/Data/Tools/ScriptUpdater/APIUpdater.NonObsoleteApiUpdaterDetector.exe
[api-updater (non-obsolete-error-filter)] 
----------------------------------
jit/startup time      : 71.9783ms
moved types parse time: 59ms
candidates parse time : 3ms
C# parse time         : 249ms
candidates check time : 70ms
console write time    : 0ms

[api-updater (non-obsolete-error-filter)] 1/16/2024 4:40:00 PM : Starting C:/Program Files/Unity/Hub/Editor/2020.3.32f1/Editor/Data/Tools/ScriptUpdater/APIUpdater.NonObsoleteApiUpdaterDetector.exe
[api-updater (non-obsolete-error-filter)] 
----------------------------------
jit/startup time      : 1245.4878ms
moved types parse time: 75ms
candidates parse time : 1ms
C# parse time         : 297ms
candidates check time : 58ms
console write time    : 1ms

[api-updater (non-obsolete-error-filter)] 1/16/2024 4:40:22 PM : Starting C:/Program Files/Unity/Hub/Editor/2020.3.32f1/Editor/Data/Tools/ScriptUpdater/APIUpdater.NonObsoleteApiUpdaterDetector.exe
[api-updater (non-obsolete-error-filter)] 
----------------------------------
jit/startup time      : 71.7542ms
moved types parse time: 54ms
candidates parse time : 1ms
C# parse time         : 193ms
candidates check time : 64ms
console write time    : 0ms

[api-updater (non-obsolete-error-filter)] 1/16/2024 4:40:33 PM : Starting C:/Program Files/Unity/Hub/Editor/2020.3.32f1/Editor/Data/Tools/ScriptUpdater/APIUpdater.NonObsoleteApiUpdaterDetector.exe
[api-updater (non-obsolete-error-filter)] 
----------------------------------
jit/startup time      : 93.9383ms
moved types parse time: 81ms
candidates parse time : 1ms
C# parse time         : 193ms
candidates check time : 36ms
console write time    : 0ms

[api-updater (non-obsolete-error-filter)] 1/16/2024 4:41:20 PM : Starting C:/Program Files/Unity/Hub/Editor/2020.3.32f1/Editor/Data/Tools/ScriptUpdater/APIUpdater.NonObsoleteApiUpdaterDetector.exe
[api-updater (non-obsolete-error-filter)] 
----------------------------------
jit/startup time      : 109.3219ms
moved types parse time: 80ms
candidates parse time : 3ms
C# parse time         : 211ms
candidates check time : 27ms
console write time    : 0ms

[api-updater (non-obsolete-error-filter)] 1/16/2024 4:43:06 PM : Starting C:/Program Files/Unity/Hub/Editor/2020.3.32f1/Editor/Data/Tools/ScriptUpdater/APIUpdater.NonObsoleteApiUpdaterDetector.exe
[api-updater (non-obsolete-error-filter)] 
----------------------------------
jit/startup time      : 113.355ms
moved types parse time: 63ms
candidates parse time : 12ms
C# parse time         : 205ms
candidates check time : 70ms
console write time    : 0ms

[api-updater (non-obsolete-error-filter)] 1/17/2024 5:58:44 PM : Starting C:/Program Files/Unity/Hub/Editor/2020.3.32f1/Editor/Data/Tools/ScriptUpdater/APIUpdater.NonObsoleteApiUpdaterDetector.exe
[api-updater (non-obsolete-error-filter)] 
----------------------------------
jit/startup time      : 1169.1807ms
moved types parse time: 55ms
candidates parse time : 1ms
C# parse time         : 270ms
candidates check time : 39ms
console write time    : 1ms

[api-updater (non-obsolete-error-filter)] 7/1/2024 9:29:57 AM : Starting C:/Program Files/Unity/Hub/Editor/2020.3.32f1/Editor/Data/Tools/ScriptUpdater/APIUpdater.NonObsoleteApiUpdaterDetector.exe
[api-updater (non-obsolete-error-filter)] 
----------------------------------
jit/startup time      : 1142.9081ms
moved types parse time: 69ms
candidates parse time : 1ms
C# parse time         : 369ms
candidates check time : 84ms
console write time    : 1ms

[api-updater (non-obsolete-error-filter)] 7/1/2024 9:30:26 AM : Starting C:/Program Files/Unity/Hub/Editor/2020.3.32f1/Editor/Data/Tools/ScriptUpdater/APIUpdater.NonObsoleteApiUpdaterDetector.exe
[api-updater (non-obsolete-error-filter)] 
----------------------------------
jit/startup time      : 65.8519ms
moved types parse time: 52ms
candidates parse time : 1ms
C# parse time         : 201ms
candidates check time : 68ms
console write time    : 0ms

[api-updater (non-obsolete-error-filter)] 7/1/2024 9:30:37 AM : Starting C:/Program Files/Unity/Hub/Editor/2020.3.32f1/Editor/Data/Tools/ScriptUpdater/APIUpdater.NonObsoleteApiUpdaterDetector.exe
[api-updater (non-obsolete-error-filter)] 
----------------------------------
jit/startup time      : 76.9574ms
moved types parse time: 51ms
candidates parse time : 1ms
C# parse time         : 180ms
candidates check time : 39ms
console write time    : 0ms

[api-updater (non-obsolete-error-filter)] 7/1/2024 9:32:21 AM : Starting C:/Program Files/Unity/Hub/Editor/2020.3.32f1/Editor/Data/Tools/ScriptUpdater/APIUpdater.NonObsoleteApiUpdaterDetector.exe
[api-updater (non-obsolete-error-filter)] 
----------------------------------
jit/startup time      : 85.1939ms
moved types parse time: 60ms
candidates parse time : 3ms
C# parse time         : 185ms
candidates check time : 36ms
console write time    : 1ms

[api-updater (non-obsolete-error-filter)] 7/1/2024 9:34:04 AM : Starting C:/Program Files/Unity/Hub/Editor/2020.3.32f1/Editor/Data/Tools/ScriptUpdater/APIUpdater.NonObsoleteApiUpdaterDetector.exe
[api-updater (non-obsolete-error-filter)] 
----------------------------------
jit/startup time      : 72.9623ms
moved types parse time: 59ms
candidates parse time : 3ms
C# parse time         : 215ms
candidates check time : 29ms
console write time    : 0ms

