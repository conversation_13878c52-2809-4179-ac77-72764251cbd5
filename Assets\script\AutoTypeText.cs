using UnityEngine;
using System.Collections;
using UnityEngine.UI;

public class AutoTypeText : MonoBehaviour
{
	public float letterPause = 0.2f;
	public AudioClip sound;
	private string word;
    public bool music = true;
	public void OnEnable()
    {
        word = GetComponent<Text>().text;
		StartCoroutine(abc());
	}
	public void OnDisable()
    {
        GetComponent<Text>().text = "";
	}
	IEnumerator abc()
    {
		yield return new WaitForSeconds (0.01f);
	GetComponent<Text>().text = "";
	StartCoroutine(TypeText());
	}

	IEnumerator TypeText()
    {
		if (music = true)
        {
			foreach (char letter in word.ToCharArray())
            {
                GetComponent<Text>().text += letter;
				if (PlayerPrefs.GetInt ("Music") == 0)
                {
				    if (sound)
                    {
					    GetComponent<AudioSource> ().PlayOneShot (sound);
				    }
				}
				yield return new WaitForSeconds (letterPause);
			}		
		}
	}
}
