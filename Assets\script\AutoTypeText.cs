using UnityEngine;
using System.Collections;
using UnityEngine.UI;

public class AutoTypeText : MonoBehaviour
{
	private string word;
	public void OnEnable()
    {
        word = GetComponent<Text>().text;
		StartCoroutine(ShowText());
	}
	public void OnDisable()
    {
        GetComponent<Text>().text = "";
	}

	IEnumerator ShowText()
    {
		GetComponent<Text>().text = "";
		yield return new WaitForSeconds(0.05f);
		GetComponent<Text>().text = word;
	}
}
