<dependencies>
  <packages>
    <package>androidx.constraintlayout:constraintlayout:2.1.4</package>
    <package>androidx.fragment:fragment:1.6.0</package>
    <package>androidx.lifecycle:lifecycle-common-java8:2.6.1</package>
    <package>androidx.lifecycle:lifecycle-process:2.6.1</package>
    <package>com.google.android.gms:play-services-ads:24.2.0</package>
    <package>com.google.android.gms:play-services-base:18.2.0</package>
    <package>com.google.android.ump:user-messaging-platform:3.2.0</package>
    <package>com.google.firebase:firebase-analytics:21.3.0</package>
    <package>com.google.firebase:firebase-analytics-unity:11.6.0</package>
    <package>com.google.firebase:firebase-app-unity:11.6.0</package>
    <package>com.google.firebase:firebase-common:20.3.3</package>
  </packages>
  <files>
    <file>Assets/GeneratedLocalRepo/Firebase/m2repository/com/google/firebase/firebase-analytics-unity/11.6.0/firebase-analytics-unity-11.6.0.aar</file>
    <file>Assets/GeneratedLocalRepo/Firebase/m2repository/com/google/firebase/firebase-analytics-unity/11.6.0/firebase-analytics-unity-11.6.0.pom</file>
    <file>Assets/GeneratedLocalRepo/Firebase/m2repository/com/google/firebase/firebase-app-unity/11.6.0/firebase-app-unity-11.6.0.aar</file>
    <file>Assets/GeneratedLocalRepo/Firebase/m2repository/com/google/firebase/firebase-app-unity/11.6.0/firebase-app-unity-11.6.0.pom</file>
  </files>
  <settings>
    <setting name="androidAbis" value="arm64-v8a,armeabi-v7a" />
    <setting name="bundleId" value="com.smg.tractor.trolly.games.farming.game" />
    <setting name="explodeAars" value="True" />
    <setting name="gradleBuildEnabled" value="True" />
    <setting name="gradlePropertiesTemplateEnabled" value="True" />
    <setting name="gradleTemplateEnabled" value="True" />
    <setting name="installAndroidPackages" value="True" />
    <setting name="localMavenRepoDir" value="Assets/GeneratedLocalRepo" />
    <setting name="packageDir" value="Assets/Plugins/Android" />
    <setting name="patchAndroidManifest" value="True" />
    <setting name="patchMainTemplateGradle" value="True" />
    <setting name="projectExportEnabled" value="False" />
    <setting name="useFullCustomMavenRepoPathWhenExport" value="True" />
    <setting name="useFullCustomMavenRepoPathWhenNotExport" value="False" />
    <setting name="useJetifier" value="True" />
  </settings>
</dependencies>