﻿using UnityEngine;
using UnityEngine.SceneManagement;

namespace SciFiArsenal
{
public class SciFiLoadSceneOnClick2 : MonoBehaviour
{
    public void LoadSceneSciFiUpdate1()  {
		SceneManager.LoadScene ("update_scifi_1");
	}
	public void LoadSceneSciFiUpdate2()  {
		SceneManager.LoadScene ("update_scifi_2");
	}
	public void LoadSceneSciFiUpdate3()  {
		SceneManager.LoadScene ("update_scifi_3");
	}
	public void LoadSceneSciFiUpdate4()  {
		SceneManager.LoadScene ("update_scifi_4");
	}
	public void LoadSceneSciFiUpdate5()  {
		SceneManager.LoadScene ("update_scifi_5");
	}
	public void LoadSceneSciFiUpdate6()  {
		SceneManager.LoadScene ("update_scifi_6");
	}
	public void LoadSceneSciFiUpdate7()  {
		SceneManager.LoadScene ("update_scifi_7");
	}
}
}