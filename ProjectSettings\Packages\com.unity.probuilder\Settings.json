{"m_Name": "Settings", "m_Path": "ProjectSettings/Packages/com.unity.probuilder/Settings.json", "m_Dictionary": {"m_DictionaryValues": [{"type": "UnityEngine.ProBuilder.LogLevel, Unity.ProBuilder, Version=0.0.0.0, Culture=neutral, PublicKeyToken=null", "key": "log.level", "value": "{\"m_Value\":3}"}, {"type": "UnityEngine.ProBuilder.LogOutput, Unity.ProBuilder, Version=0.0.0.0, Culture=neutral, PublicKeyToken=null", "key": "log.output", "value": "{\"m_Value\":1}"}, {"type": "System.String, mscorlib, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089", "key": "log.path", "value": "{\"m_Value\":\"ProBuilderLog.txt\"}"}, {"type": "UnityEngine.ProBuilder.SemVer, Unity.ProBuilder, Version=0.0.0.0, Culture=neutral, PublicKeyToken=null", "key": "about.identifier", "value": "{\"m_Value\":{\"m_Major\":5,\"m_Minor\":2,\"m_Patch\":3,\"m_Build\":-1,\"m_Type\":\"\",\"m_Metadata\":\"\",\"m_Date\":\"\"}}"}, {"type": "UnityEngine.ProBuilder.SemVer, Unity.ProBuilder, Version=0.0.0.0, Culture=neutral, PublicKeyToken=null", "key": "preferences.version", "value": "{\"m_Value\":{\"m_Major\":5,\"m_Minor\":2,\"m_Patch\":3,\"m_Build\":-1,\"m_Type\":\"\",\"m_Metadata\":\"\",\"m_Date\":\"\"}}"}, {"type": "<PERSON>.<PERSON>, mscorl<PERSON>, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089", "key": "lightmapping.autoUnwrapLightmapUV", "value": "{\"m_Value\":true}"}, {"type": "<PERSON>.<PERSON>, mscorl<PERSON>, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089", "key": "editor.autoRecalculateCollisions", "value": "{\"m_Value\":false}"}, {"type": "<PERSON>.<PERSON>, mscorl<PERSON>, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089", "key": "editor.stripProBuilderScriptsOnBuild", "value": "{\"m_Value\":true}"}, {"type": "<PERSON>.<PERSON>, mscorl<PERSON>, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089", "key": "experimental.enabled", "value": "{\"m_Value\":false}"}]}}