using System.Collections;
using System.Collections.Generic;
using UnityEngine;
using UnityEngine.UI;
public class player : MonoBehaviour
{
    public GameObject endcam, trolly1, startpositn, fillbar, effect, birdmusic, watersound, arrow, sellersound, fountain, engn;
    public GameObject[] traffic;
    public Animator anim, play;
    public GameObject[] Music;
    void Start()
    {
        Time.timeScale = 1;
    }
    public void OnTriggerEnter(Collider collision)
    {
        if (collision.gameObject.tag == "checkpoint")
        {
            collision.gameObject.GetComponent<BoxCollider>().enabled = false;
            collision.gameObject.transform.GetChild(0).gameObject.SetActive(false);
            collision.gameObject.transform.GetChild(1).gameObject.SetActive(true);
        }
        if (collision.gameObject.tag == "finish")
        {
            Destroy(collision.gameObject);
            StartCoroutine("end");
        }
        if (collision.gameObject.tag == "finish1")
        {
            SMGGameManager.Instance.player.gameObject.GetComponent<Rigidbody>().drag = 5.5f;
            SMGGameManager.Instance.player.gameObject.GetComponent<Rigidbody>().isKinematic = true;
            SMGGameManager.Instance.player.GetComponent<RCC_CarControllerV3>().enabled = false;
            Destroy(collision.gameObject);
            SMGGameManager.Instance.gamplybtn.SetActive(false);
            SMGGameManager.Instance.rcccanvec.SetActive(false);
            SMGGameManager.Instance.levlcam[MainMenu.levlno].SetActive(true);
            StartCoroutine("end1");
        }
        if (collision.gameObject.tag == "open")
        {
            anim.enabled = true;
        }
        if (collision.gameObject.tag == "close")
        {
            arrow.SetActive(false);
        }
        if (collision.gameObject.tag == "close1")
        {
            sellersound.SetActive(true);
        }
        if (collision.gameObject.tag == "soundtrue")
        {
            fountain.SetActive(true);
        }
        if (collision.gameObject.tag == "vehicl")
        {
            traffic[MainMenu.levlno].SetActive(true);
        }
        if (collision.gameObject.tag == "dstry")
        {
            Destroy(collision.gameObject);
            fillbar.SetActive(true);
        }
        if (collision.gameObject.tag == "water")
        {
            collision.gameObject.GetComponent<BoxCollider>().enabled = false;
            watersound.SetActive(true);
        }
        if (collision.gameObject.tag == "wood")
        {
            SMGGameManager.Instance.player.gameObject.GetComponent<Rigidbody>().drag = 5.5f;
            SMGGameManager.Instance.gamplybtn.SetActive(false);
            collision.gameObject.GetComponent<BoxCollider>().enabled = false;
            SMGGameManager.Instance.bpnl.SetActive(true);
            SMGGameManager.Instance.woods[MainMenu.levlno].SetActive(true);
            StartCoroutine("lvl6");
        }
        if (collision.gameObject.tag == "waterof")
        {
            collision.gameObject.GetComponent<BoxCollider>().enabled = false;
            watersound.SetActive(false);
            fountain.SetActive(false);
        }
    }
    public IEnumerator end()
    {
        engn.SetActive(false);
        SMGGameManager.Instance.player.gameObject.GetComponent<Rigidbody>().drag = 5.5f;
        SMGGameManager.Instance.player.gameObject.GetComponent<Rigidbody>().isKinematic = true;
        SMGGameManager.Instance.player.GetComponent<RCC_CarControllerV3>().enabled = false;
        effect.SetActive(false);
        SMGGameManager.Instance.gamplybtn.SetActive(false);
        SMGGameManager.Instance.rcccanvec.SetActive(false);
        yield return new WaitForSeconds(0.5f);
        birdmusic.SetActive(false);
        foreach (GameObject m in Music)
        {
            m.SetActive(false);
        }
        endcam.SetActive(true);
        yield return new WaitForSeconds(6f);
        endcam.SetActive(false);
        sellersound.SetActive(false);
        SMGGameManager.Instance.completpnl.SetActive(true);
       
        yield return new WaitForSeconds(2f);
        PlayerPrefs.SetInt("Level" + MainMenu.levlno, 1);
        PlayerPrefs.SetInt("coins", PlayerPrefs.GetInt("coins") + 500);
       
    }
    IEnumerator end1()
    {
        engn.SetActive(false);
        yield return new WaitForSeconds(6f);
        SMGGameManager.Instance.trolly.SetActive(false);
        SMGGameManager.Instance.player.transform.position = startpositn.transform.position;
        SMGGameManager.Instance.player.transform.rotation = startpositn.transform.rotation;
        trolly1.SetActive(true);
        yield return new WaitForSeconds(6f);
        SMGGameManager.Instance.levlcam[MainMenu.levlno].SetActive(false);
        endcam.SetActive(true);
        birdmusic.SetActive(false);
        foreach (GameObject m in Music)
        {
            m.SetActive(false);
        }
        yield return new WaitForSeconds(6f);
        endcam.SetActive(false);
        SMGGameManager.Instance.completpnl.SetActive(true);
       
        yield return new WaitForSeconds(2f);
        PlayerPrefs.SetInt("Level" + MainMenu.levlno, 1);
        PlayerPrefs.SetInt("coins", PlayerPrefs.GetInt("coins") + 500);
       
    }
    IEnumerator lvl6()
    {
        yield return new WaitForSeconds(2f);
        SMGGameManager.Instance.player.gameObject.GetComponent<Rigidbody>().drag = 0.05f;
        SMGGameManager.Instance.gamplybtn.SetActive(true);
        SMGGameManager.Instance.bpnl.SetActive(false);
        play.enabled = false;
    }
}
