fileFormatVersion: 2
guid: 146346ade5a3ada469b6363cba5f061a
ModelImporter:
  serializedVersion: 20300
  internalIDToNameTable:
  - first:
      1: 100000
    second: //RootNode
  - first:
      1: 100002
    second: Chicken
  - first:
      1: 100004
    second: CHICKEN_
  - first:
      1: 100006
    second: CHICKE<PERSON>_ Head
  - first:
      1: 100008
    second: CHICKEN_ L Calf
  - first:
      1: 100010
    second: CHICKEN_ L Clavicle
  - first:
      1: 100012
    second: CHICKEN_ L Foot
  - first:
      1: 100014
    second: CHICKEN_ L Forearm
  - first:
      1: 100016
    second: CHICKEN_ L Hand
  - first:
      1: 100018
    second: CHICKEN_ L HorseLink
  - first:
      1: 100020
    second: CHICKEN_ L Thigh
  - first:
      1: 100022
    second: CHICKEN_ L Toe0
  - first:
      1: 100024
    second: CHICKEN_ L Toe01
  - first:
      1: 100026
    second: CHICKEN_ L Toe1
  - first:
      1: 100028
    second: CHICKEN_ L Toe11
  - first:
      1: 100030
    second: CHICKEN_ L Toe2
  - first:
      1: 100032
    second: CHICKEN_ L Toe21
  - first:
      1: 100034
    second: CHICKEN_ L Toe3
  - first:
      1: 100036
    second: CHICKEN_ L Toe31
  - first:
      1: 100038
    second: CHICKEN_ L UpperArm
  - first:
      1: 100040
    second: CHICKEN_ Neck
  - first:
      1: 100042
    second: CHICKEN_ Neck1
  - first:
      1: 100044
    second: CHICKEN_ Neck2
  - first:
      1: 100046
    second: CHICKEN_ Pelvis
  - first:
      1: 100048
    second: CHICKEN_ Queue de cheval 1
  - first:
      1: 100050
    second: CHICKEN_ R Calf
  - first:
      1: 100052
    second: CHICKEN_ R Clavicle
  - first:
      1: 100054
    second: CHICKEN_ R Foot
  - first:
      1: 100056
    second: CHICKEN_ R Forearm
  - first:
      1: 100058
    second: CHICKEN_ R Hand
  - first:
      1: 100060
    second: CHICKEN_ R HorseLink
  - first:
      1: 100062
    second: CHICKEN_ R Thigh
  - first:
      1: 100064
    second: CHICKEN_ R Toe0
  - first:
      1: 100066
    second: CHICKEN_ R Toe01
  - first:
      1: 100068
    second: CHICKEN_ R Toe1
  - first:
      1: 100070
    second: CHICKEN_ R Toe11
  - first:
      1: 100072
    second: CHICKEN_ R Toe2
  - first:
      1: 100074
    second: CHICKEN_ R Toe21
  - first:
      1: 100076
    second: CHICKEN_ R Toe3
  - first:
      1: 100078
    second: CHICKEN_ R Toe31
  - first:
      1: 100080
    second: CHICKEN_ R UpperArm
  - first:
      1: 100082
    second: CHICKEN_ Spine
  - first:
      1: 100084
    second: CHICKEN_ Tail
  - first:
      1: 100086
    second: root
  - first:
      1: 100088
    second: Chicken_LOD0
  - first:
      1: 100090
    second: Chicken_LOD1
  - first:
      4: 400000
    second: //RootNode
  - first:
      4: 400002
    second: Chicken
  - first:
      4: 400004
    second: CHICKEN_
  - first:
      4: 400006
    second: CHICKEN_ Head
  - first:
      4: 400008
    second: CHICKEN_ L Calf
  - first:
      4: 400010
    second: CHICKEN_ L Clavicle
  - first:
      4: 400012
    second: CHICKEN_ L Foot
  - first:
      4: 400014
    second: CHICKEN_ L Forearm
  - first:
      4: 400016
    second: CHICKEN_ L Hand
  - first:
      4: 400018
    second: CHICKEN_ L HorseLink
  - first:
      4: 400020
    second: CHICKEN_ L Thigh
  - first:
      4: 400022
    second: CHICKEN_ L Toe0
  - first:
      4: 400024
    second: CHICKEN_ L Toe01
  - first:
      4: 400026
    second: CHICKEN_ L Toe1
  - first:
      4: 400028
    second: CHICKEN_ L Toe11
  - first:
      4: 400030
    second: CHICKEN_ L Toe2
  - first:
      4: 400032
    second: CHICKEN_ L Toe21
  - first:
      4: 400034
    second: CHICKEN_ L Toe3
  - first:
      4: 400036
    second: CHICKEN_ L Toe31
  - first:
      4: 400038
    second: CHICKEN_ L UpperArm
  - first:
      4: 400040
    second: CHICKEN_ Neck
  - first:
      4: 400042
    second: CHICKEN_ Neck1
  - first:
      4: 400044
    second: CHICKEN_ Neck2
  - first:
      4: 400046
    second: CHICKEN_ Pelvis
  - first:
      4: 400048
    second: CHICKEN_ Queue de cheval 1
  - first:
      4: 400050
    second: CHICKEN_ R Calf
  - first:
      4: 400052
    second: CHICKEN_ R Clavicle
  - first:
      4: 400054
    second: CHICKEN_ R Foot
  - first:
      4: 400056
    second: CHICKEN_ R Forearm
  - first:
      4: 400058
    second: CHICKEN_ R Hand
  - first:
      4: 400060
    second: CHICKEN_ R HorseLink
  - first:
      4: 400062
    second: CHICKEN_ R Thigh
  - first:
      4: 400064
    second: CHICKEN_ R Toe0
  - first:
      4: 400066
    second: CHICKEN_ R Toe01
  - first:
      4: 400068
    second: CHICKEN_ R Toe1
  - first:
      4: 400070
    second: CHICKEN_ R Toe11
  - first:
      4: 400072
    second: CHICKEN_ R Toe2
  - first:
      4: 400074
    second: CHICKEN_ R Toe21
  - first:
      4: 400076
    second: CHICKEN_ R Toe3
  - first:
      4: 400078
    second: CHICKEN_ R Toe31
  - first:
      4: 400080
    second: CHICKEN_ R UpperArm
  - first:
      4: 400082
    second: CHICKEN_ Spine
  - first:
      4: 400084
    second: CHICKEN_ Tail
  - first:
      4: 400086
    second: root
  - first:
      4: 400088
    second: Chicken_LOD0
  - first:
      4: 400090
    second: Chicken_LOD1
  - first:
      43: 4300000
    second: Chicken
  - first:
      43: 4300002
    second: Chicken_LOD0
  - first:
      43: 4300004
    second: Chicken_LOD1
  - first:
      74: 7400000
    second: Take 001
  - first:
      95: 9500000
    second: //RootNode
  - first:
      137: 13700000
    second: Chicken
  - first:
      137: 13700002
    second: Chicken_LOD0
  - first:
      137: 13700004
    second: Chicken_LOD1
  - first:
      205: 20500000
    second: //RootNode
  externalObjects: {}
  materials:
    materialImportMode: 1
    materialName: 0
    materialSearch: 1
    materialLocation: 0
  animations:
    legacyGenerateAnimations: 4
    bakeSimulation: 0
    resampleCurves: 1
    optimizeGameObjects: 0
    motionNodeName: 
    rigImportErrors: 
    rigImportWarnings: 
    animationImportErrors: 
    animationImportWarnings: 
    animationRetargetingWarnings: 
    animationDoRetargetingWarnings: 0
    importAnimatedCustomProperties: 0
    importConstraints: 0
    animationCompression: 1
    animationRotationError: 0.5
    animationPositionError: 0.5
    animationScaleError: 0.5
    animationWrapMode: 0
    extraExposedTransformPaths: []
    extraUserProperties: []
    clipAnimations: []
    isReadable: 1
  meshes:
    lODScreenPercentages:
    - 0.25
    - 0.01
    globalScale: 1
    meshCompression: 3
    addColliders: 0
    useSRGBMaterialColor: 1
    sortHierarchyByName: 1
    importVisibility: 0
    importBlendShapes: 1
    importCameras: 0
    importLights: 0
    fileIdsGeneration: 1
    swapUVChannels: 0
    generateSecondaryUV: 0
    useFileUnits: 1
    keepQuads: 0
    weldVertices: 1
    bakeAxisConversion: 0
    preserveHierarchy: 0
    skinWeightsMode: 0
    maxBonesPerVertex: 4
    minBoneWeight: 0.001
    meshOptimizationFlags: -1
    indexFormat: 1
    secondaryUVAngleDistortion: 8
    secondaryUVAreaDistortion: 15.000001
    secondaryUVHardAngle: 88
    secondaryUVMarginMethod: 0
    secondaryUVMinLightmapResolution: 40
    secondaryUVMinObjectScale: 1
    secondaryUVPackMargin: 4
    useFileScale: 1
  tangentSpace:
    normalSmoothAngle: 60
    normalImportMode: 0
    tangentImportMode: 4
    normalCalculationMode: 0
    legacyComputeAllNormalsFromSmoothingGroupsWhenMeshHasBlendShapes: 1
    blendShapeNormalImportMode: 1
    normalSmoothingSource: 0
  referencedClips:
  - 2cfb41c43f8042f4da913168426473e1
  - cb5a6ca35429f414aa70e553a467b354
  - c5fb20eec64d0d24893c2ace799affb7
  - f5ba9f28eff35164dab2fbd8f456ad0f
  - f4726052c0660334bb37b9078d128ab4
  - d993f0c7abbcf534d865bdca89e9ca80
  - da7cfecd0f8778c4e9fd3a61b9ea897b
  - c871536b66c9e03419293314c6f775ee
  - 6afe035445175d14f8e3e4a0c61eb7a9
  - dd65eb71c574b554f95ecd2828fe8515
  - 1b1db27bf5f0c8f459c2ca6a48301a1a
  importAnimation: 1
  humanDescription:
    serializedVersion: 3
    human: []
    skeleton: []
    armTwist: 0.5
    foreArmTwist: 0.5
    upperLegTwist: 0.5
    legTwist: 0.5
    armStretch: 0.05
    legStretch: 0.05
    feetSpacing: 0
    globalScale: 1
    rootMotionBoneName: root
    hasTranslationDoF: 0
    hasExtraRoot: 0
    skeletonHasParents: 0
  lastHumanDescriptionAvatarSource: {instanceID: 0}
  autoGenerateAvatarMappingIfUnspecified: 1
  animationType: 2
  humanoidOversampling: 1
  avatarSetup: 1
  addHumanoidExtraRootOnlyWhenUsingAvatar: 0
  remapMaterialsIfMaterialImportModeIsNone: 1
  additionalBone: 0
  userData: 
  assetBundleName: 
  assetBundleVariant: 
